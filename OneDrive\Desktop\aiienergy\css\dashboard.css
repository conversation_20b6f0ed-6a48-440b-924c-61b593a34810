:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 70px;
    --card-border-radius: 12px;
    --transition-speed: 0.3s;
    --chart-height: 300px;
    --success-color: #4CAF50;
    --warning-color: #FFC107;
    --danger-color: #F44336;
    --info-color: #2196F3;
    
    /* تعريف ألوان مشاريع الطاقة */
    --solar-color: #FFA726;
    --wind-color: #29B6F6;
    --hydro-color: #42A5F5;
    --geothermal-color: #EF5350;
    --biomass-color: #66BB6A;
    --other-color: #78909C;
}

/* تنسيق عام للوحة التحكم */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--background-dark);
    color: var(--text-color);
}

/* تنسيق الشريط الجانبي */
.sidebar {
    width: var(--sidebar-width);
    background: rgba(10,10,15,0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 100;
    transition: width var(--transition-speed) ease;
    border-left: 1px solid rgba(255, 114, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 5px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 10px;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-info {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}

.user-avatar ion-icon {
    font-size: 30px;
    color: var(--secondary-color);
}

.user-details h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.user-details p {
    margin: 5px 0 0;
    font-size: 14px;
    opacity: 0.7;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: block;
}

.sidebar-nav li {
    margin: 0;
    padding: 0;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.sidebar-nav a:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav li.active a {
    background: rgba(255, 114, 0, 0.2);
    border-right: 3px solid var(--primary-color);
}

.sidebar-nav a ion-icon {
    font-size: 20px;
    margin-left: 15px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
    display: flex;
    align-items: center;
    background: transparent;
    border: 1px solid var(--primary-color);
    color: var(--text-color);
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 15px;
}

.logout-btn:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
}

.logout-btn ion-icon {
    margin-left: 10px;
    font-size: 18px;
}

.theme-label {
    margin-right: 10px;
    font-size: 14px;
}

/* تنسيق المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    padding: 20px;
    transition: margin var(--transition-speed) ease;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 500;
}

#current-date {
    margin: 5px 0 0;
    font-size: 14px;
    opacity: 0.7;
}

.header-right {
    display: flex;
    align-items: center;
}

.search-bar {
    position: relative;
    margin-left: 20px;
}

.search-bar input {
    width: 250px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 0 40px 0 15px;
    color: var(--text-color);
    font-size: 14px;
}

.search-bar button {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 18px;
    cursor: pointer;
}

.notification-btn {
    position: relative;
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
}

.notification-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: var(--primary-color);
    color: var(--secondary-color);
    font-size: 10px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تنسيق بطاقات الإحصائيات */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--card-border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}

.stat-icon ion-icon {
    font-size: 30px;
    color: var(--secondary-color);
}

.stat-info h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.stat-value {
    margin: 5px 0;
    font-size: 24px;
    font-weight: 700;
}

.stat-change {
    margin: 0;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* تنسيق صفوف لوحة التحكم */
.dashboard-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container, .recent-projects, .ai-insights {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--card-border-radius);
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.chart-header, .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h2, .section-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.chart-actions {
    display: flex;
}

.chart-action-btn, .refresh-btn {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    margin-right: 5px;
}

.chart {
    height: var(--chart-height);
    position: relative;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
}

/* تنسيق قسم المشاريع */
.content-section {
    margin-top: 30px;
}

.content-section.hidden {
    display: none;
}

.primary-btn {
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.primary-btn ion-icon {
    margin-left: 5px;
}

.primary-btn:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.projects-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-size: 14px;
    margin-bottom: 5px;
}

.filter-group select, .search-filter input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    padding: 8px 12px;
    color: var(--text-color);
    min-width: 150px;
}

.search-filter {
    flex-grow: 1;
}

.search-filter input {
    width: 100%;
}

/* تنسيق النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--form-bg);
    border-radius: var(--card-border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 114, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
}

.close-modal-btn {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 24px;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.secondary-btn {
    background: transparent;
    border: 1px solid var(--primary-color);
    color: var(--text-color);
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background: rgba(255, 114, 0, 0.1);
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 992px) {
    .dashboard-row {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        width: var(--sidebar-collapsed-width);
    }
    
    .sidebar.expanded {
        width: var(--sidebar-width);
    }
    
    .main-content {
        margin-right: var(--sidebar-collapsed-width);
    }
    
    .sidebar.expanded + .main-content {
        margin-right: var(--sidebar-width);
    }
    
    .sidebar:not(.expanded) .user-details,
    .sidebar:not(.expanded) .sidebar-nav a span,
    .sidebar:not(.expanded) .logout-btn span,
    .sidebar:not(.expanded) .theme-label {
        display: none;
    }
    
    .sidebar:not(.expanded) .sidebar-nav a {
        justify-content: center;
    }
    
    .sidebar:not(.expanded) .sidebar-nav a ion-icon {
        margin: 0;
    }
}

@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .content-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-right {
        margin-top: 15px;
        width: 100%;
    }
    
    .search-bar {
        width: 100%;
        margin-left: 0;
    }
    
    .search-bar input {
        width: 100%;
    }
}
