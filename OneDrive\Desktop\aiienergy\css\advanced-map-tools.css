/* ===== ADVANCED MAP TOOLS CSS ===== */

/* حاوي الخريطة الرئيسي */
.google-maps-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-radius: 20px;
    padding: 25px;
    margin: 30px 0;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 114, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.google-maps-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff7200 0%, #ff9500 50%, #ffb700 100%);
    border-radius: 20px 20px 0 0;
}

.google-maps-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 114, 0, 0.2);
}

/* عنوان الخريطة */
.map-title {
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
    background: linear-gradient(135deg, #ff7200 0%, #ff9500 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.map-title::after {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #ff7200 0%, #ff9500 100%);
    border-radius: 2px;
}

/* حاوي الخريطة */
#google-map {
    width: 100%;
    height: 500px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.2),
        inset 0 0 0 1px rgba(255, 114, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

#google-map:hover {
    box-shadow: 
        0 20px 45px rgba(0, 0, 0, 0.3),
        inset 0 0 0 1px rgba(255, 114, 0, 0.2);
}

/* أدوات التحكم في الخريطة */
.map-controls {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.map-control-btn {
    width: 45px;
    height: 45px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 114, 0, 0.3);
    border-radius: 12px;
    color: #ff7200;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.map-control-btn:hover {
    background: rgba(255, 114, 0, 0.9);
    border-color: #ff7200;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(255, 114, 0, 0.4);
}

.map-control-btn ion-icon {
    font-size: 20px;
}

/* أزرار التكبير */
.zoom-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 1000;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 114, 0, 0.3);
    border-radius: 10px;
    color: #ff7200;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    backdrop-filter: blur(10px);
}

.zoom-btn:hover {
    background: rgba(255, 114, 0, 0.9);
    border-color: #ff7200;
    color: white;
    transform: scale(1.1);
}

/* مؤشر نوع الخريطة */
.map-type-indicator {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 114, 0, 0.3);
    border-radius: 20px;
    padding: 8px 15px;
    color: #ff7200;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.map-type-indicator:hover {
    background: rgba(255, 114, 0, 0.1);
    border-color: #ff7200;
}

/* شريط أدوات الرسم */
.drawing-toolbar {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid rgba(255, 114, 0, 0.3);
    border-radius: 15px;
    padding: 10px;
    display: flex;
    gap: 8px;
    z-index: 1000;
    backdrop-filter: blur(15px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.drawing-tool {
    width: 35px;
    height: 35px;
    background: transparent;
    border: 1px solid rgba(255, 114, 0, 0.3);
    border-radius: 8px;
    color: #ff7200;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.drawing-tool:hover,
.drawing-tool.active {
    background: rgba(255, 114, 0, 0.2);
    border-color: #ff7200;
    color: white;
    transform: scale(1.1);
}

/* معلومات الخريطة */
.map-info-panel {
    position: absolute;
    top: 80px;
    left: 15px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid rgba(255, 114, 0, 0.3);
    border-radius: 12px;
    padding: 15px;
    color: white;
    font-size: 12px;
    max-width: 200px;
    z-index: 1000;
    backdrop-filter: blur(15px);
    transform: translateX(-100%);
    transition: all 0.3s ease;
}

.map-info-panel.show {
    transform: translateX(0);
}

.map-info-panel h4 {
    color: #ff7200;
    margin: 0 0 8px 0;
    font-size: 14px;
}

.map-info-panel p {
    margin: 4px 0;
    opacity: 0.9;
}

/* وضع ملء الشاشة */
.map-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    border-radius: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

.map-fullscreen #google-map {
    height: 100vh !important;
    border-radius: 0 !important;
}

.map-fullscreen .map-controls {
    top: 20px;
    right: 20px;
}

/* مؤشر التحميل */
.map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
}

.map-loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 114, 0, 0.3);
    border-top: 4px solid #ff7200;
    border-radius: 50%;
    animation: mapSpin 1s linear infinite;
}

@keyframes mapSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* إشعارات الخريطة */
.map-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ff7200;
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    backdrop-filter: blur(15px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

/* استجابة للشاشات المختلفة */
@media (max-width: 768px) {
    .google-maps-card {
        padding: 15px;
        margin: 20px 0;
        border-radius: 15px;
    }
    
    #google-map {
        height: 400px;
        border-radius: 12px;
    }
    
    .map-title {
        font-size: 20px;
        margin-bottom: 15px;
    }
    
    .map-controls {
        top: 10px;
        right: 10px;
        gap: 8px;
    }
    
    .map-control-btn {
        width: 40px;
        height: 40px;
        border-radius: 10px;
    }
    
    .map-control-btn ion-icon {
        font-size: 18px;
    }
    
    .drawing-toolbar {
        top: 10px;
        left: 10px;
        padding: 8px;
        gap: 6px;
    }
    
    .drawing-tool {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
    
    .zoom-controls {
        bottom: 15px;
        right: 15px;
    }
    
    .zoom-btn {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .google-maps-card {
        padding: 10px;
        margin: 15px 0;
    }
    
    #google-map {
        height: 300px;
    }
    
    .map-title {
        font-size: 18px;
    }
    
    .map-info-panel {
        max-width: 150px;
        padding: 10px;
        font-size: 11px;
    }
    
    .map-notification {
        font-size: 12px;
        padding: 10px 15px;
    }
}
