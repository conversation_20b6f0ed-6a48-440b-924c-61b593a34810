/* خلفية فضائية مبسطة وواضحة */

/* طبقة ضبابية عامة للخلفية - DISABLED TO FIX TEXT BLUR */
html::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 14, 26, 0.02); /* Reduced opacity */
    /* backdrop-filter: blur(5px); DISABLED */
    /* -webkit-backdrop-filter: blur(5px); DISABLED */
    pointer-events: none;
    z-index: -10; /* Much lower z-index */
}

/* الخلفية الأساسية */
html, body {
    background: #0a0e1a !important;
    background-image:
        radial-gradient(ellipse at top left, rgba(64, 224, 255, 0.2) 0%, transparent 50%),
        radial-gradient(ellipse at top right, rgba(255, 119, 48, 0.15) 0%, transparent 50%),
        radial-gradient(ellipse at bottom left, rgba(120, 119, 198, 0.25) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(0, 191, 255, 0.15) 0%, transparent 50%) !important;
    background-attachment: fixed !important;
    min-height: 100vh !important;
}

/* طبقة ضبابية إضافية للخلفية فقط - DISABLED TO FIX TEXT BLUR */
html::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 14, 26, 0.03); /* Reduced opacity */
    /* filter: blur(2px); DISABLED */
    pointer-events: none;
    z-index: -9; /* Much lower z-index */
}

/* النجوم المتحركة */
body::before {
    content: '' !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-image:
        radial-gradient(2px 2px at 25px 25px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 75px 75px, rgba(64,224,255,0.8), transparent),
        radial-gradient(2px 2px at 125px 25px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 175px 75px, rgba(255,119,48,0.6), transparent),
        radial-gradient(1px 1px at 225px 125px, rgba(255,255,255,0.8), transparent),
        radial-gradient(2px 2px at 50px 150px, rgba(120,119,198,0.7), transparent) !important;
    background-repeat: repeat !important;
    background-size: 250px 200px !important;
    animation: sparkle 25s linear infinite !important;
    pointer-events: none !important;
    z-index: -8 !important; /* Much lower z-index */
    opacity: 0.9 !important;
    filter: blur(2.5px) !important;
}

/* الكوكب المتوهج */
body::after {
    content: '' !important;
    position: fixed !important;
    bottom: -150px !important;
    right: -150px !important;
    width: 300px !important;
    height: 300px !important;
    background:
        radial-gradient(circle at 40% 40%, rgba(64, 224, 255, 0.4) 0%, rgba(33, 150, 243, 0.2) 50%, transparent 80%),
        radial-gradient(circle at 60% 60%, rgba(255, 119, 48, 0.3) 0%, transparent 60%) !important;
    border-radius: 50% !important;
    animation: planetGlow 10s ease-in-out infinite alternate !important;
    pointer-events: none !important;
    z-index: -7 !important; /* Much lower z-index */
    box-shadow:
        0 0 50px rgba(64, 224, 255, 0.3),
        0 0 100px rgba(64, 224, 255, 0.15) !important;
    filter: blur(3px) !important;
}

/* الحركات */
@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(250px); }
}

@keyframes planetGlow {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 0.7;
    }
    100% {
        transform: scale(1.2) rotate(15deg);
        opacity: 0.9;
    }
}

/* إخفاء التأثيرات الافتراضية في الثيم الفاتح */
[data-theme="light"] html::before,
[data-theme="light"] html::after {
    display: none !important;
}

[data-theme="light"] html,
[data-theme="light"] body {
    background:
        radial-gradient(ellipse 800px 600px at top left, rgba(25, 118, 210, 0.12) 0%, transparent 60%),
        radial-gradient(ellipse 700px 500px at top right, rgba(255, 152, 0, 0.10) 0%, transparent 55%),
        radial-gradient(ellipse 600px 400px at bottom left, rgba(33, 150, 243, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse 750px 550px at bottom right, rgba(76, 175, 80, 0.06) 0%, transparent 55%),
        radial-gradient(ellipse 900px 700px at center, rgba(156, 39, 176, 0.04) 0%, transparent 70%),
        linear-gradient(135deg,
            #ffffff 0%,
            #f8f9fa 15%,
            #e3f2fd 30%,
            #f1f8e9 45%,
            #fff3e0 60%,
            #fce4ec 75%,
            #f3e5f5 90%,
            #ffffff 100%) !important;
    background-attachment: fixed !important;
    animation: lightBackgroundShift 20s ease-in-out infinite !important;
}

/* انيميشن الخلفية للوضع الفاتح */
@keyframes lightBackgroundShift {
    0%, 100% {
        background:
            radial-gradient(ellipse 800px 600px at top left, rgba(25, 118, 210, 0.12) 0%, transparent 60%),
            radial-gradient(ellipse 700px 500px at top right, rgba(255, 152, 0, 0.10) 0%, transparent 55%),
            radial-gradient(ellipse 600px 400px at bottom left, rgba(33, 150, 243, 0.08) 0%, transparent 50%),
            radial-gradient(ellipse 750px 550px at bottom right, rgba(76, 175, 80, 0.06) 0%, transparent 55%),
            radial-gradient(ellipse 900px 700px at center, rgba(156, 39, 176, 0.04) 0%, transparent 70%),
            linear-gradient(135deg, #ffffff 0%, #f8f9fa 15%, #e3f2fd 30%, #f1f8e9 45%, #fff3e0 60%, #fce4ec 75%, #f3e5f5 90%, #ffffff 100%);
    }
    25% {
        background:
            radial-gradient(ellipse 850px 650px at top right, rgba(25, 118, 210, 0.14) 0%, transparent 65%),
            radial-gradient(ellipse 750px 550px at bottom left, rgba(255, 152, 0, 0.12) 0%, transparent 60%),
            radial-gradient(ellipse 650px 450px at top left, rgba(33, 150, 243, 0.10) 0%, transparent 55%),
            radial-gradient(ellipse 800px 600px at bottom right, rgba(76, 175, 80, 0.08) 0%, transparent 60%),
            radial-gradient(ellipse 950px 750px at center, rgba(156, 39, 176, 0.06) 0%, transparent 75%),
            linear-gradient(225deg, #f8f9fa 0%, #e3f2fd 15%, #f1f8e9 30%, #fff3e0 45%, #fce4ec 60%, #f3e5f5 75%, #e8eaf6 90%, #ffffff 100%);
    }
    50% {
        background:
            radial-gradient(ellipse 900px 700px at bottom right, rgba(25, 118, 210, 0.16) 0%, transparent 70%),
            radial-gradient(ellipse 800px 600px at top left, rgba(255, 152, 0, 0.14) 0%, transparent 65%),
            radial-gradient(ellipse 700px 500px at bottom left, rgba(33, 150, 243, 0.12) 0%, transparent 60%),
            radial-gradient(ellipse 850px 650px at top right, rgba(76, 175, 80, 0.10) 0%, transparent 65%),
            radial-gradient(ellipse 1000px 800px at center, rgba(156, 39, 176, 0.08) 0%, transparent 80%),
            linear-gradient(315deg, #e3f2fd 0%, #f1f8e9 15%, #fff3e0 30%, #fce4ec 45%, #f3e5f5 60%, #e8eaf6 75%, #f8f9fa 90%, #ffffff 100%);
    }
    75% {
        background:
            radial-gradient(ellipse 750px 550px at bottom left, rgba(25, 118, 210, 0.14) 0%, transparent 65%),
            radial-gradient(ellipse 650px 450px at bottom right, rgba(255, 152, 0, 0.12) 0%, transparent 60%),
            radial-gradient(ellipse 800px 600px at top right, rgba(33, 150, 243, 0.10) 0%, transparent 55%),
            radial-gradient(ellipse 700px 500px at top left, rgba(76, 175, 80, 0.08) 0%, transparent 60%),
            radial-gradient(ellipse 950px 750px at center, rgba(156, 39, 176, 0.06) 0%, transparent 75%),
            linear-gradient(45deg, #f1f8e9 0%, #fff3e0 15%, #fce4ec 30%, #f3e5f5 45%, #e8eaf6 60%, #f8f9fa 75%, #e3f2fd 90%, #ffffff 100%);
    }
}

/* التأكد من شفافية المحتوى */
.main {
    background: transparent !important;
    position: relative !important;
    z-index: 100 !important;
}

/* تطبيق الخلفية على جميع الأقسام */
.section {
    background: transparent !important;
    position: relative !important;
    z-index: 100 !important;
}

/* تأثيرات إضافية للأقسام */
.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse 600px 300px at 20% 50%, rgba(64, 224, 255, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.service-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse 600px 300px at 80% 50%, rgba(255, 119, 48, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.design-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse 600px 300px at 50% 50%, rgba(120, 119, 198, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse 600px 300px at 60% 30%, rgba(0, 191, 255, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

/* تأثيرات نجوم إضافية لكل قسم */
.about-section::after {
    content: '';
    position: absolute;
    top: 20%;
    right: 10%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(64, 224, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

.service-section::after {
    content: '';
    position: absolute;
    top: 30%;
    left: 15%;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(255, 119, 48, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 3s ease-in-out infinite reverse;
    pointer-events: none;
    z-index: 1;
}

.design-section::after {
    content: '';
    position: absolute;
    bottom: 20%;
    right: 20%;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(120, 119, 198, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 5s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
}

/* جعل الهيدر شفاف ومتناسق مع الخلفية الفضائية */
.navbar {
    background: rgba(10, 14, 26, 0.85) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid rgba(64, 224, 255, 0.2) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

/* تأثير توهج للهيدر */
.navbar::before {
    background: linear-gradient(to bottom, rgba(64, 224, 255, 0.1), transparent) !important;
}

.navbar::after {
    background: linear-gradient(to right, transparent, rgba(64, 224, 255, 0.4), transparent) !important;
    height: 1px !important;
}

/* تحسين شفافية الهيدر في الثيم الفاتح */
[data-theme="light"] .navbar {
    background: rgba(245, 247, 250, 0.9) !important;
    border-bottom: 1px solid rgba(66, 165, 245, 0.3) !important;
}

[data-theme="light"] .navbar::before {
    background: linear-gradient(to bottom, rgba(66, 165, 245, 0.1), transparent) !important;
}

[data-theme="light"] .navbar::after {
    background: linear-gradient(to right, transparent, rgba(66, 165, 245, 0.4), transparent) !important;
}

/* تحسينات إضافية للخلفية المتحركة في الوضع الفاتح */
[data-theme="light"] body::before {
    content: '' !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background:
        radial-gradient(circle 300px at 20% 30%, rgba(25, 118, 210, 0.08) 0%, transparent 50%),
        radial-gradient(circle 250px at 80% 70%, rgba(255, 152, 0, 0.06) 0%, transparent 50%),
        radial-gradient(circle 200px at 60% 20%, rgba(76, 175, 80, 0.05) 0%, transparent 50%),
        radial-gradient(circle 350px at 30% 80%, rgba(156, 39, 176, 0.04) 0%, transparent 50%) !important;
    animation: lightOverlayShift 30s ease-in-out infinite !important;
    pointer-events: none !important;
    z-index: -8 !important; /* Much lower z-index */
    opacity: 0.7 !important;
}

[data-theme="light"] body::after {
    content: '' !important;
    position: fixed !important;
    bottom: -100px !important;
    right: -100px !important;
    width: 200px !important;
    height: 200px !important;
    background:
        radial-gradient(circle at 40% 40%, rgba(25, 118, 210, 0.3) 0%, rgba(33, 150, 243, 0.15) 50%, transparent 80%),
        radial-gradient(circle at 60% 60%, rgba(255, 152, 0, 0.2) 0%, transparent 60%) !important;
    border-radius: 50% !important;
    animation: lightPlanetGlow 15s ease-in-out infinite alternate !important;
    pointer-events: none !important;
    z-index: -7 !important; /* Much lower z-index */
    box-shadow:
        0 0 40px rgba(25, 118, 210, 0.2),
        0 0 80px rgba(25, 118, 210, 0.1) !important;
    filter: blur(2px) !important;
}

@keyframes lightOverlayShift {
    0%, 100% {
        transform: translateX(0px) translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    25% {
        transform: translateX(20px) translateY(-15px) rotate(5deg);
        opacity: 0.9;
    }
    50% {
        transform: translateX(-10px) translateY(25px) rotate(-3deg);
        opacity: 0.8;
    }
    75% {
        transform: translateX(15px) translateY(-10px) rotate(2deg);
        opacity: 0.85;
    }
}

@keyframes lightPlanetGlow {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 0.5;
    }
    100% {
        transform: scale(1.3) rotate(20deg);
        opacity: 0.8;
    }
}

/* تأثيرات جسيمات متحركة للوضع الفاتح */
[data-theme="light"] .floating-particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(25, 118, 210, 0.8), transparent),
        radial-gradient(1px 1px at 40px 70px, rgba(255, 152, 0, 0.7), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(76, 175, 80, 0.6), transparent),
        radial-gradient(2px 2px at 130px 80px, rgba(156, 39, 176, 0.5), transparent),
        radial-gradient(1px 1px at 160px 30px, rgba(244, 67, 54, 0.6), transparent),
        radial-gradient(2px 2px at 200px 60px, rgba(103, 58, 183, 0.7), transparent);
    background-repeat: repeat;
    background-size: 250px 200px;
    animation: lightSparkle 20s linear infinite;
    pointer-events: none;
    z-index: 2;
    opacity: 0.8;
}

@keyframes lightSparkle {
    from { transform: translateX(0) translateY(0); }
    to { transform: translateX(250px) translateY(-100px); }
}

/* موجات ضوئية متحركة للوضع الفاتح */
[data-theme="light"] .energy-waves::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle 400px at 25% 25%, rgba(25, 118, 210, 0.1) 0%, transparent 50%),
        radial-gradient(circle 300px at 75% 75%, rgba(255, 152, 0, 0.08) 0%, transparent 50%),
        radial-gradient(circle 350px at 50% 10%, rgba(76, 175, 80, 0.06) 0%, transparent 50%),
        radial-gradient(circle 250px at 10% 90%, rgba(156, 39, 176, 0.05) 0%, transparent 50%);
    animation: lightWaveFlow 25s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes lightWaveFlow {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.6;
    }
    25% {
        transform: scale(1.1) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(0.9) rotate(180deg);
        opacity: 0.7;
    }
    75% {
        transform: scale(1.05) rotate(270deg);
        opacity: 0.9;
    }
}

/* تأثيرات ضوئية نابضة للوضع الفاتح */
[data-theme="light"] .twinkling-stars::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle 150px at 15% 20%, rgba(25, 118, 210, 0.15) 0%, transparent 70%),
        radial-gradient(circle 120px at 85% 30%, rgba(255, 152, 0, 0.12) 0%, transparent 70%),
        radial-gradient(circle 100px at 30% 80%, rgba(76, 175, 80, 0.10) 0%, transparent 70%),
        radial-gradient(circle 130px at 70% 70%, rgba(156, 39, 176, 0.08) 0%, transparent 70%);
    animation: lightGlow 8s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: 2;
}

@keyframes lightGlow {
    0% {
        transform: scale(1);
        opacity: 0.4;
    }
    100% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

/* خطوط طاقة ديناميكية للوضع الفاتح */
[data-theme="light"] .energy-lines::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 40%, rgba(25, 118, 210, 0.3) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(255, 152, 0, 0.25) 50%, transparent 60%),
        linear-gradient(135deg, transparent 40%, rgba(76, 175, 80, 0.2) 50%, transparent 60%);
    background-size: 200px 200px, 150px 150px, 180px 180px;
    animation: lightEnergyFlow 15s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes lightEnergyFlow {
    0% {
        background-position: 0 0, 0 0, 0 0;
        opacity: 0.6;
    }
    50% {
        background-position: 200px 200px, -150px 150px, 180px -180px;
        opacity: 0.9;
    }
    100% {
        background-position: 400px 400px, -300px 300px, 360px -360px;
        opacity: 0.6;
    }
}

/* أشكال هندسية متحركة للوضع الفاتح */
[data-theme="light"] .geometric-shapes::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 200px;
    height: 200px;
    background:
        linear-gradient(45deg, rgba(25, 118, 210, 0.2) 0%, transparent 50%),
        linear-gradient(-45deg, rgba(255, 152, 0, 0.15) 0%, transparent 50%);
    border-radius: 50%;
    animation: lightGeometricFloat 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -6; /* Much lower z-index */
    filter: blur(1px);
}

[data-theme="light"] .geometric-shapes::after {
    content: '';
    position: absolute;
    top: 60%;
    right: 15%;
    width: 150px;
    height: 150px;
    background:
        conic-gradient(from 0deg, rgba(76, 175, 80, 0.2), rgba(156, 39, 176, 0.15), rgba(244, 67, 54, 0.1), rgba(76, 175, 80, 0.2));
    border-radius: 30%;
    animation: lightGeometricSpin 25s linear infinite;
    pointer-events: none;
    z-index: -5; /* Much lower z-index */
    filter: blur(0.5px);
}

@keyframes lightGeometricFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) translateX(-15px) rotate(180deg) scale(0.9);
        opacity: 0.7;
    }
    75% {
        transform: translateY(20px) translateX(10px) rotate(270deg) scale(1.05);
        opacity: 0.9;
    }
}

@keyframes lightGeometricSpin {
    from {
        transform: rotate(0deg) scale(1);
        opacity: 0.5;
    }
    50% {
        transform: rotate(180deg) scale(1.2);
        opacity: 0.8;
    }
    to {
        transform: rotate(360deg) scale(1);
        opacity: 0.5;
    }
}

/* تأثيرات نبضة متقدمة للوضع الفاتح */
[data-theme="light"] .pulse-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    margin: -150px 0 0 -150px;
    background:
        radial-gradient(circle at center, rgba(25, 118, 210, 0.15) 0%, rgba(255, 152, 0, 0.1) 30%, transparent 70%);
    border-radius: 50%;
    animation: lightPulseExpand 12s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

[data-theme="light"] .pulse-effect::after {
    content: '';
    position: absolute;
    top: 30%;
    right: 20%;
    width: 100px;
    height: 100px;
    background:
        radial-gradient(circle at center, rgba(76, 175, 80, 0.2) 0%, rgba(156, 39, 176, 0.1) 50%, transparent 80%);
    border-radius: 50%;
    animation: lightPulseContract 8s ease-in-out infinite reverse;
    pointer-events: none;
    z-index: 2;
}

@keyframes lightPulseExpand {
    0%, 100% {
        transform: scale(1);
        opacity: 0.4;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.8;
    }
}

@keyframes lightPulseContract {
    0%, 100% {
        transform: scale(1.2);
        opacity: 0.6;
    }
    50% {
        transform: scale(0.8);
        opacity: 0.9;
    }
}

/* تحسين شفافية المحتوى للوضع الفاتح */
[data-theme="light"] .main {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 0 !important;
    box-shadow:
        0 8px 32px rgba(25, 118, 210, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}
