/**
 * Enhanced Gemini-style Chat Interface
 * واجهة المحادثة المحسنة بنمط Gemini
 */

.gemini-chat-modal {
    position: fixed;
    inset: 0;
    background: #0d1117;
    z-index: 10002;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.gemini-chat-modal.open {
    opacity: 1;
    pointer-events: auto;
}

.gemini-chat-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    max-width: 768px;
    margin: 0 auto;
    position: relative;
}

/* Header Styles */
.gemini-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(13, 17, 23, 0.95);
    backdrop-filter: blur(10px);
}

.gemini-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #e6edf3;
    font-size: 18px;
    font-weight: 500;
}

.gemini-logo::before {
    content: "✨";
    font-size: 24px;
}

.gemini-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.gemini-model-selector {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 6px 12px;
    color: #e6edf3;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.gemini-model-selector:hover {
    background: rgba(255, 255, 255, 0.15);
}

.gemini-close {
    background: none;
    border: none;
    color: #7d8590;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.gemini-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #e6edf3;
}

/* Messages Container */
.gemini-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* Welcome Screen */
.gemini-welcome {
    text-align: center;
    color: #7d8590;
    margin-top: auto;
    margin-bottom: auto;
}

.gemini-welcome h2 {
    color: #58a6ff;
    font-size: 32px;
    margin-bottom: 8px;
    font-weight: 400;
}

.gemini-welcome p {
    font-size: 16px;
    margin-bottom: 24px;
}

/* Suggestion Cards */
.gemini-suggestions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    max-width: 600px;
    margin: 0 auto;
}

.suggestion-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: right;
}

.suggestion-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(88, 166, 255, 0.3);
    transform: translateY(-2px);
}

.suggestion-card h4 {
    color: #e6edf3;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
}

.suggestion-card p {
    color: #7d8590;
    font-size: 12px;
    line-height: 1.4;
}

/* Message Bubbles */
.message-bubble {
    max-width: 80%;
    margin-bottom: 16px;
    animation: fadeInUp 0.3s ease;
}

.message-bubble.user {
    align-self: flex-end;
    background: rgba(88, 166, 255, 0.1);
    border: 1px solid rgba(88, 166, 255, 0.3);
    border-radius: 18px 18px 4px 18px;
    padding: 12px 16px;
    color: #e6edf3;
}

.message-bubble.assistant {
    align-self: flex-start;
    background: transparent;
    color: #e6edf3;
    padding: 12px 0;
    line-height: 1.6;
}

/* Input Container */
.gemini-input-container {
    padding: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(13, 17, 23, 0.95);
    backdrop-filter: blur(10px);
}

.gemini-input-wrapper {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.gemini-input {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    padding: 12px 50px 12px 20px;
    color: #e6edf3;
    font-size: 16px;
    outline: none;
    transition: all 0.2s ease;
    resize: none;
    min-height: 48px;
    max-height: 120px;
    font-family: inherit;
}

.gemini-input:focus {
    border-color: rgba(88, 166, 255, 0.5);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
}

.gemini-input::placeholder {
    color: #7d8590;
}

.gemini-send-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: #58a6ff;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
}

.gemini-send-btn:hover:not(:disabled) {
    background: #4493e0;
    transform: translateY(-50%) scale(1.05);
}

.gemini-send-btn:disabled {
    background: #30363d;
    cursor: not-allowed;
    transform: translateY(-50%);
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7d8590;
    font-size: 14px;
    padding: 12px 0;
    animation: fadeInUp 0.3s ease;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: #7d8590;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { 
        transform: scale(0.8); 
        opacity: 0.5; 
    }
    40% { 
        transform: scale(1); 
        opacity: 1; 
    }
}

/* Input Tools */
.gemini-input-tools {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.input-tool {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 6px 12px;
    color: #7d8590;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.input-tool:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #e6edf3;
    transform: translateY(-1px);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scrollbar Styling */
.gemini-messages::-webkit-scrollbar {
    width: 6px;
}

.gemini-messages::-webkit-scrollbar-track {
    background: transparent;
}

.gemini-messages::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.gemini-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .gemini-chat-container {
        max-width: 100%;
    }
    
    .gemini-header {
        padding: 12px 16px;
    }
    
    .gemini-messages {
        padding: 16px;
    }
    
    .gemini-input-container {
        padding: 16px;
    }
    
    .gemini-suggestions {
        grid-template-columns: 1fr;
    }
    
    .gemini-welcome h2 {
        font-size: 24px;
    }
    
    .message-bubble {
        max-width: 90%;
    }
    
    .gemini-input-tools {
        gap: 6px;
    }
    
    .input-tool {
        padding: 4px 8px;
        font-size: 11px;
    }
}

/* Dark theme enhancements */
@media (prefers-color-scheme: dark) {
    .gemini-chat-modal {
        background: #0d1117;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gemini-input {
        border-color: rgba(255, 255, 255, 0.5);
    }
    
    .suggestion-card {
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .message-bubble.user {
        border-color: rgba(88, 166, 255, 0.6);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .gemini-chat-modal,
    .suggestion-card,
    .gemini-send-btn,
    .input-tool {
        transition: none;
    }
    
    .typing-dot {
        animation: none;
    }
    
    @keyframes fadeInUp {
        from, to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}
