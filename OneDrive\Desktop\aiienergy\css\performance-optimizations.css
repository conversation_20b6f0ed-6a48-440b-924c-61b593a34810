/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Critical CSS - Above the fold content */
:root {
    /* Performance Variables */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --border-radius: 8px;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* GPU Acceleration for smooth animations */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Optimize font loading */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local('Roboto'), local('Roboto-Regular'),
         url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2') format('woff2');
}

/* Critical above-the-fold styles */
.main {
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: var(--transition-fast);
    will-change: transform;
}

/* Optimize images */
img {
    max-width: 100%;
    height: auto;
    loading: lazy;
    decoding: async;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Intersection Observer optimizations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Lazy loading placeholders */
.lazy-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Optimize scrolling */
.smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Container queries for responsive design */
@container (min-width: 768px) {
    .responsive-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }
}

/* Critical loading states */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Optimize chat interface */
.ai-chat-container {
    contain: layout style paint;
    transform: translateZ(0);
}

.chat-messages {
    overflow-y: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    contain: strict;
}

/* Optimize form inputs */
input, textarea, select {
    will-change: border-color, box-shadow;
    transition: var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

/* Optimize buttons */
button {
    cursor: pointer;
    transition: var(--transition-fast);
    will-change: transform, box-shadow;
}

button:hover {
    transform: translateY(-2px);
}

button:active {
    transform: translateY(0);
}

/* Optimize modal animations */
.modal {
    opacity: 0;
    visibility: hidden;
    transform: scale(0.9);
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

/* Critical responsive breakpoints */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 1rem;
    }
    
    .content h1 {
        font-size: 2.5rem;
    }
    
    .contact-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

/* Optimize dark/light theme transitions */
[data-theme="light"] {
    --background-primary: #ffffff;
    --text-primary: #333333;
    --primary-color: #1976d2;
}

[data-theme="dark"] {
    --background-primary: #0a0a0a;
    --text-primary: #ffffff;
    --primary-color: #42a5f5;
}

/* Preload critical resources */
.preload-critical {
    position: absolute;
    width: 1px;
    height: 1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
}

/* Optimize map container */
.map-container {
    contain: strict;
    height: 400px;
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Service Worker support indicator */
.sw-update-available {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    z-index: 10000;
    transform: translateY(100px);
    transition: transform var(--transition-normal);
}

.sw-update-available.show {
    transform: translateY(0);
}

/* Optimize language switching */
.language-toggle {
    contain: layout;
}

.lang-btn {
    transition: var(--transition-fast);
    will-change: background-color, color;
}

/* Critical error states */
.error-message {
    background: #f44336;
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Success states */
.success-message {
    background: #4caf50;
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Loading Screen Styles */
.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    margin-bottom: 2rem;
}

.loading-logo .site-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

.loading-logo .site-logo::before {
    content: "⚡";
    font-size: 2rem;
    color: white;
}

.loading-logo h2 {
    font-size: 2rem;
    margin: 0;
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-text {
    margin-top: 1rem;
    font-size: 1.1rem;
    opacity: 0.8;
    animation: fadeInOut 2s infinite;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Enhanced notification styles */
.notification-container {
    font-family: 'Roboto', sans-serif;
}

.notification {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Optimize print styles */
@media print {
    .navbar,
    .ai-chat-container,
    .fixed-cta-container,
    .animated-background,
    .loading-screen,
    .notification-container {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}
