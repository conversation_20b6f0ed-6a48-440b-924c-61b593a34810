/* تنسيق عام لواجهة اختبار API */
.api-tester-container {
    padding: 80px 20px 40px;
    max-width: 1400px;
    margin: 0 auto;
    color: var(--text-color);
}

.api-tester-header {
    text-align: center;
    margin-bottom: 30px;
}

.api-tester-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.api-tester-header p {
    font-size: 1.1rem;
    opacity: 0.8;
}

.api-tester-content {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 20px;
    background: rgba(10, 10, 15, 0.5);
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid rgba(255, 114, 0, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* تنسيق الشريط الجانبي */
.api-sidebar {
    background: rgba(10, 10, 15, 0.7);
    border-left: 1px solid rgba(255, 114, 0, 0.2);
    padding: 20px 0;
    height: 100%;
    overflow-y: auto;
}

.api-endpoints h3 {
    padding: 0 20px;
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: var(--primary-color);
}

.endpoint-group {
    margin-bottom: 15px;
}

.endpoint-group-title {
    padding: 10px 20px;
    font-weight: 500;
    color: var(--text-color);
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
}

.endpoint-group ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.endpoint {
    padding: 8px 20px 8px 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background 0.3s;
}

.endpoint:hover {
    background: rgba(255, 114, 0, 0.1);
}

.method {
    display: inline-block;
    width: 60px;
    text-align: center;
    padding: 3px 0;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: 10px;
}

.method.get {
    background: rgba(97, 175, 254, 0.1);
    color: #61affe;
}

.method.post {
    background: rgba(73, 204, 144, 0.1);
    color: #49cc90;
}

.method.put {
    background: rgba(252, 161, 48, 0.1);
    color: #fca130;
}

.method.delete {
    background: rgba(249, 62, 62, 0.1);
    color: #f93e3e;
}

.endpoint-path {
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تنسيق لوحة الطلب */
.api-request-panel {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 114, 0, 0.2);
}

.request-header {
    margin-bottom: 20px;
}

.request-method-url {
    display: flex;
    gap: 10px;
}

#request-method {
    width: 100px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 0.9rem;
}

#request-url {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 0.9rem;
}

.primary-btn {
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.primary-btn:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.secondary-btn {
    background: transparent;
    color: var(--text-color);
    border: 1px solid var(--primary-color);
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.secondary-btn:hover {
    background: rgba(255, 114, 0, 0.1);
}

.request-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.tab {
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s;
    border-bottom: 2px solid transparent;
}

.tab:hover {
    color: var(--primary-color);
}

.tab.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.tab-content {
    display: none;
    padding: 15px 0;
}

.tab-content.active {
    display: block;
}

/* تنسيق المعلمات والرؤوس والجسم */
.param-row, .header-row, .form-data-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.param-key, .param-value, .header-key, .header-value, .form-data-key, .form-data-value {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 0.9rem;
}

.remove-param, .remove-header, .remove-form-data {
    background: rgba(249, 62, 62, 0.1);
    color: #f93e3e;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s;
}

.remove-param:hover, .remove-header:hover, .remove-form-data:hover {
    background: rgba(249, 62, 62, 0.3);
}

.body-type-selector, .auth-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.body-type-selector label, .auth-type-selector label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.body-content-container > div, .auth-content-container > div {
    display: none;
}

.body-content-container > div.active, .auth-content-container > div.active {
    display: block;
}

#json-editor {
    width: 100%;
    height: 200px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-color);
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    resize: vertical;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

/* تنسيق لوحة الاستجابة */
.api-response-panel {
    padding: 20px;
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.response-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.response-status {
    display: flex;
    gap: 15px;
}

#response-status-code {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

#response-status-code.success {
    background: rgba(73, 204, 144, 0.1);
    color: #49cc90;
}

#response-status-code.error {
    background: rgba(249, 62, 62, 0.1);
    color: #f93e3e;
}

#response-time {
    font-size: 0.9rem;
    opacity: 0.7;
}

.response-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.response-content {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    overflow: hidden;
}

#response-body, #response-headers {
    padding: 15px;
    margin: 0;
    max-height: 400px;
    overflow: auto;
    font-family: monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color);
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 992px) {
    .api-tester-content {
        grid-template-columns: 1fr;
    }
    
    .api-sidebar {
        border-left: none;
        border-bottom: 1px solid rgba(255, 114, 0, 0.2);
    }
}

@media (max-width: 768px) {
    .request-method-url {
        flex-direction: column;
    }
    
    .body-type-selector, .auth-type-selector {
        flex-direction: column;
        gap: 10px;
    }
}
