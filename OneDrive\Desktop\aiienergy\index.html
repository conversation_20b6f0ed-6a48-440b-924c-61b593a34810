قم <!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta name="description" content="Energy.AI - Smart Energy Solutions with AI-powered optimization. Reduce energy costs by 15-30% with our intelligent energy management systems. Advanced renewable energy solutions for businesses and homes in Jordan and MENA region.">
    <parameter name="keywords" content="energy AI, smart energy solutions, renewable energy, energy optimization, artificial intelligence, energy management, cost reduction, sustainability, Jordan energy, MENA energy solutions, solar power, wind energy, smart grid, IoT energy, energy analytics, carbon footprint reduction">
    <meta name="author" content="Energy.AI - Mohammad <PERSON>">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="language" content="en, ar">
    <meta name="theme-color" content="#1976d2">
    <meta name="msapplication-TileColor" content="#1976d2">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Energy.AI">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://energy-ai.netlify.app/">
    <meta property="og:title" content="Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization">
    <meta property="og:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Smart energy management for businesses and homes. Advanced renewable energy solutions with real-time monitoring and predictive analytics.">
    <meta property="og:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta property="og:image:width" content="512">
    <meta property="og:image:height" content="512">
    <meta property="og:image:alt" content="Energy.AI Logo - Smart Energy Solutions">
    <meta property="og:site_name" content="Energy.AI">
    <meta property="og:locale" content="en_US">
    <meta property="og:locale:alternate" content="ar_JO">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://energy-ai.netlify.app/">
    <meta name="twitter:title" content="Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization">
    <meta name="twitter:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Advanced renewable energy management systems.">
    <meta name="twitter:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta name="twitter:image:alt" content="Energy.AI - Smart Energy Solutions">
    <meta name="twitter:creator" content="@EnergyAI_Jordan">
    <meta name="twitter:site" content="@EnergyAI_Jordan">

    <!-- Canonical and Alternate Languages -->
    <link rel="canonical" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="en" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="ar" href="https://energy-ai.netlify.app/?lang=ar">
    <link rel="alternate" hreflang="x-default" href="https://energy-ai.netlify.app/">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/icon-192x192.png">
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">

    <title>Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization</title>

    <!-- Enhanced Gemini Chat Styles -->
    <link rel="stylesheet" href="css/gemini-chat.css">

    <!-- Modern Glassmorphism CSS - Inline for performance -->
    <style>
        /* Modern Glassmorphism Critical Styles */
        :root {
            --primary-color: #1976d2;
            --secondary-color: #ff7200;
            --background-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.15);
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
        }

        [data-theme="light"] {
            --background-primary: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #ddeeff 50%, #d4e9ff 75%, #cce4ff 100%);
            --text-primary: #2c3e50;
            --text-secondary: #5d4e37;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.3);
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background-primary);
            background-attachment: fixed;
            color: var(--text-primary);
            overflow-x: hidden;
            transition: all var(--transition-normal);
        }

        /* Glassmorphism Background */
        .glassmorphism-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--background-primary);
        }

        /* Floating Shapes */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 114, 0, 0.08), rgba(25, 118, 210, 0.08));
            border-radius: 50%;
            animation: float 25s infinite linear;
        }

        .shape:nth-child(1) { width: 120px; height: 120px; top: 15%; left: 8%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 180px; height: 180px; top: 65%; left: 85%; animation-delay: 8s; }
        .shape:nth-child(3) { width: 90px; height: 90px; top: 85%; left: 15%; animation-delay: 16s; }
        .shape:nth-child(4) { width: 150px; height: 150px; top: 25%; left: 75%; animation-delay: 24s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(-40px) rotate(90deg) scale(1.1); }
            50% { transform: translateY(-20px) rotate(180deg) scale(0.9); }
            75% { transform: translateY(-60px) rotate(270deg) scale(1.05); }
        }

        /* Glass Components */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .main {
            min-height: 100vh;
            position: relative;
        }

        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all var(--transition-normal);
        }

        /* Critical inline styles for immediate loading */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--background-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.8s ease;
            overflow: hidden;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }
    </style>

    <!-- Critical CSS - Load immediately -->
    <link rel="stylesheet" href="css/glassmorphism.css">
    <link rel="stylesheet" href="css/welcome-screen.css">

    <!-- CSS Files - Load non-critical CSS asynchronously -->
    <link rel="preload" href="css/performance-optimizations.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="css/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="css/space-background.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="stylesheet" href="css/icons.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/naya.css">
    <link rel="stylesheet" href="css/enhanced-map-animations.css">
    <link rel="stylesheet" href="css/neon-cursor.css">

    <!-- Fallback for browsers that don't support preload -->
    <noscript>
        <link rel="stylesheet" href="css/performance-optimizations.css">
        <link rel="stylesheet" href="css/styles.css">
        <link rel="stylesheet" href="css/space-background.css">
        <link rel="stylesheet" href="css/welcome-screen.css">
    </noscript>


    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Glassmorphism Background -->
    <div class="glassmorphism-bg"></div>

    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Welcome Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="welcome-logo-section">
                <div class="welcome-logo">
                    <div class="site-logo large-logo"></div>
                    <div class="logo-glow"></div>
                </div>
                <h1 class="welcome-title" data-en="Welcome to" data-ar="مرحباً بك في">Welcome to</h1>
                <h2 class="brand-name" data-en="Energy.AI" data-ar="Energy.AI">Energy.AI</h2>
                <p class="welcome-subtitle" data-en="Smart Energy Solutions Powered by AI" data-ar="حلول الطاقة الذكية مدعومة بالذكاء الاصطناعي">Smart Energy Solutions Powered by AI</p>
            </div>
            <div class="welcome-animation">
                <div class="energy-particles"></div>
                <div class="loading-progress">
                    <div class="progress-bar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main">
        <nav class="navbar glass">
            <div class="nav-content">
                <a href="#home" class="nav-logo">
                    <div class="nav-logo-icon">⚡</div>
                    <span>Energy.AI</span>
                </a>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home" class="nav-link active" data-translate="Home">Home</a></li>
                    <li><a href="#about" class="nav-link" data-translate="About">About</a></li>
                    <li><a href="#service" class="nav-link" data-translate="Services">Services</a></li>
                    <li><a href="#design" class="nav-link" data-translate="Design">Design</a></li>
                    <li><a href="#contact" class="nav-link" data-translate="Contact">Contact</a></li>
                </ul>
                <div class="nav-actions">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <ion-icon name="menu-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </nav>
        <section class="hero" id="home">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-line-1">Web Design &</span>
                    <span class="title-line-2">Development</span>
                    <span class="title-line-3">Energy</span>
                </h1>
                <p class="hero-description">
                    AI is the spark igniting a new era of energy innovation<br>
                    powering tomorrow with<br>
                    intelligent solutions today
                </p>
                <div class="hero-actions">
                    <button class="btn btn-primary" id="joinBtn" data-translate="Join Us">
                        <ion-icon name="person-add-outline"></ion-icon>
                        Join Us
                    </button>
                    <button class="btn btn-secondary" data-translate="Book Free Consultation">Book Free Consultation</button>
                </div>
            </div>

            <!-- Floating Glass Elements -->
            <div class="floating-glass floating-glass-1"></div>
            <div class="floating-glass floating-glass-2"></div>
            <div class="floating-glass floating-glass-3"></div>
        </section>

        <!-- Auth Modal will be created by auth-system.js -->

        <!-- Fixed CTA Button -->
        <div class="fixed-cta-container">
            <button class="fixed-cta-btn" id="fixedCtaBtn" data-en="get-free-consultation" data-ar="احصل على استشارة مجانية">
                <ion-icon name="call-outline"></ion-icon>
                <span>Get Free Consultation</span>
            </button>
        </div>

        <!-- AI Chat Interface -->


        <section id="about" class="section">
            <h2 data-translate="About Energy.AI">About Energy.AI</h2>
            <div class="cards-grid">
                <div class="glass-card">
                    <div class="card-icon">⚡</div>
                    <h3 data-translate="Smart Energy Management">Smart Energy Management</h3>
                    <p data-translate="AI-powered solutions to optimize energy consumption in real-time.">AI-powered solutions to optimize energy consumption in real-time.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">🌱</div>
                    <h3 data-translate="Sustainable Solutions">Sustainable Solutions</h3>
                    <p data-translate="Eco-friendly approaches to energy production and distribution.">Eco-friendly approaches to energy production and distribution.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">📊</div>
                    <h3 data-translate="Data-Driven Insights">Data-Driven Insights</h3>
                    <p data-translate="Comprehensive analytics to make informed energy decisions.">Comprehensive analytics to make informed energy decisions.</p>
                </div>
            </div>
        </section>

        <section id="service" class="section">
            <h2 data-translate="Our Services">Our Services</h2>
            <div class="cards-grid">
                <div class="glass-card">
                    <div class="card-icon">💡</div>
                    <h3 id="energyOptimizationBtn" data-translate="Energy Optimization" role="button" tabindex="0" style="cursor:pointer">Energy Optimization</h3>
                    <p data-translate="Our AI algorithms analyze your energy consumption patterns and suggest optimizations to reduce waste and cost.">Our AI algorithms analyze your energy consumption patterns and suggest optimizations to reduce waste and cost.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">📈</div>
                    <h3 data-translate="Predictive Maintenance">Predictive Maintenance</h3>
                    <p data-translate="Prevent equipment failures before they happen with our predictive maintenance solutions powered by machine learning.">Prevent equipment failures before they happen with our predictive maintenance solutions powered by machine learning.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">🛡️</div>
                    <h3 data-translate="Energy Security">Energy Security</h3>
                    <p data-translate="Protect your energy infrastructure with advanced threat detection and response systems.">Protect your energy infrastructure with advanced threat detection and response systems.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">☁️</div>
                    <h3 data-translate="Cloud Energy Management">Cloud Energy Management</h3>
                    <p data-translate="Access your energy data and controls from anywhere with our secure cloud-based platform.">Access your energy data and controls from anywhere with our secure cloud-based platform.</p>
                </div>
            </div>
        </section>

        <div id="design" class="section design-section">
            <h2 data-translate="Our Design Approach">Our Design Approach</h2>
            <div class="design-gallery">
                <div class="design-card">
                    <div class="design-image">
                        <ion-icon name="home-outline"></ion-icon>
                    </div>
                    <h3 data-translate="Smart Home Integration">Smart Home Integration</h3>
                    <p data-translate="Seamless connection between your energy systems and smart home devices.">Seamless connection between your energy systems and smart home devices.</p>
                </div>

                <div class="design-card map-card">
                    <div class="design-image map-container">
                        <div id="embedded-map" class="embedded-map"></div>
                        <div class="map-overlay">
                            <h4 data-translate="Energy.Ai Maps">Energy.Ai Maps</h4>
                            <button class="map-expand-btn" id="expandMapBtn">
                                <ion-icon name="expand-outline"></ion-icon>
                                View Full Map
                            </button>
                        </div>
                    </div>
                    <h3 data-translate="Energy.Ai Maps">Energy.Ai Maps</h3>
                    <p data-translate="Interactive mapping interface with real-time energy data visualization and location-based analysis.">Interactive mapping interface with real-time energy data visualization and location-based analysis.</p>
                </div>
            </div>
        </div>

        <div id="contact" class="section contact-section">
            <div class="contact-header">
                <h2 data-translate="Get In Touch">Get In Touch</h2>
                <p class="contact-subtitle" data-translate="Ready to transform your energy future? Let's start the conversation.">Ready to transform your energy future? Let's start the conversation.</p>
            </div>

            <!-- Free Consultation Highlight Box -->
            <div class="consultation-highlight">
                <div class="consultation-content">
                    <div class="consultation-icon">
                        <ion-icon name="bulb-outline"></ion-icon>
                    </div>
                    <div class="consultation-text">
                        <h3 data-translate="Get Your Free Energy Consultation">Get Your Free Energy Consultation</h3>
                        <p data-translate="Our experts will analyze your energy needs and provide customized solutions to reduce costs by 15-30%.">Our experts will analyze your energy needs and provide customized solutions to reduce costs by 15-30%.</p>
                        <ul class="consultation-benefits">
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Free energy audit and assessment</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Customized energy optimization plan</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>ROI analysis and cost savings projection</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="contact-container">
                <div class="contact-form-wrapper">
                    <div class="contact-form">
                        <h3 data-translate="Send us a Message">Send us a Message</h3>
                        <form id="contactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-name">
                                        <ion-icon name="person-outline"></ion-icon>
                                        <span data-translate="Full Name">Full Name</span>
                                    </label>
                                    <input type="text" id="contact-name" placeholder="Enter your full name" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-company">
                                        <ion-icon name="business-outline"></ion-icon>
                                        <span data-translate="Company">Company</span>
                                    </label>
                                    <input type="text" id="contact-company" placeholder="Your company name">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-email">
                                        <ion-icon name="mail-outline"></ion-icon>
                                        <span data-translate="Email Address">Email Address</span>
                                    </label>
                                    <input type="email" id="contact-email" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-phone">
                                        <ion-icon name="call-outline"></ion-icon>
                                        <span data-translate="Phone Number">Phone Number</span>
                                    </label>
                                    <input type="tel" id="contact-phone" placeholder="+962 XXX XXX XXX">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contact-subject">
                                    <ion-icon name="chatbubble-outline"></ion-icon>
                                    <span data-translate="Subject">Subject</span>
                                </label>
                                <select id="contact-subject" required>
                                    <option value="">Select a topic</option>
                                    <option value="consultation" selected>Free Consultation</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="partnership">Partnership</option>
                                    <option value="support">Technical Support</option>
                                    <option value="demo">Request Demo</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="contact-message">
                                    <ion-icon name="document-text-outline"></ion-icon>
                                    <span data-translate="Message">Message</span>
                                </label>
                                <textarea id="contact-message" rows="6" placeholder="Tell us about your energy needs and how we can help..." required></textarea>
                            </div>

                            <button type="submit" class="submit-btn" id="contactSubmitBtn">
                                <span class="btn-text" data-translate="Send Message">Send Message</span>
                                <ion-icon name="paper-plane-outline"></ion-icon>
                            </button>
                            <div id="contactFormStatus" class="form-status"></div>
                        </form>
                    </div>
                </div>

                <div class="contact-info-wrapper">
                    <div class="contact-info">
                        <h3>Contact Information</h3>
                        <p class="info-description">Connect with our energy experts today</p>

                        <div class="info-items">
                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="location-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Office Location</h4>
                                    <p>Amman, Jordan</p>
                                    <span>Middle East Headquarters</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="mail-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Email Address</h4>
                                    <p><EMAIL></p>
                                    <span>We reply within 24 hours</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="call-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Phone Number</h4>
                                    <p>+962 79 155 6430</p>
                                    <span>Mon - Fri, 9:00 AM - 6:00 PM</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="time-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Business Hours</h4>
                                    <p>Sunday - Thursday</p>
                                    <span>9:00 AM - 6:00 PM (GMT+3)</span>
                                </div>
                            </div>
                        </div>

                        <div class="contact-map">
                            <div class="map-placeholder">
                                <ion-icon name="map-outline"></ion-icon>
                                <p>Interactive Map</p>
                                <button class="map-btn" onclick="openContactMap()">View Location</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Modal -->
    <div class="map-modal" id="mapModal">
        <div class="map-modal-content">
            <div class="map-modal-header">
                <h3>Energy.Ai Maps - Interactive View</h3>
                <button class="map-close-btn" id="closeMapModal">
                    <ion-icon name="close-outline"></ion-icon>
                </button>
            </div>
            <div id="full-map" class="full-map"></div>
        </div>
    </div>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <div class="footer-brand-icon"></div>
                <h2>Energy.Ai</h2>
                <p>Powering the future with intelligent solutions</p>
            </div>
            <div class="footer-links">
                <h3>Links</h3>
                <ul>
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#service" id="footerServiceLink">Services</a></li>
                    <li><a href="#design">Design</a></li>
                    <li><a href="#contact">Contact</a></li>
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="faq.html">FAQ</a></li>
                </ul>
            </div>
            <div class="footer-social">
                <h3>Follow Us</h3>
                <div class="social-icons">
                    <a href="#" aria-label="Facebook"><ion-icon name="logo-facebook"></ion-icon></a>
                    <a href="#" aria-label="Instagram"><ion-icon name="logo-instagram"></ion-icon></a>
                    <a href="#" aria-label="Twitter"><ion-icon name="logo-twitter"></ion-icon></a>
                </div>
            </div>
            <div class="footer-newsletter">
                <h3>Newsletter</h3>
                <p>Stay updated with our latest news</p>
                <div class="newsletter-form">
                    <input type="email" placeholder="Your Email Address" aria-label="Email for newsletter">
                    <button aria-label="Subscribe">
                        <ion-icon name="paper-plane-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 Energy.AI. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Ionicons -->
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

    <!-- Critical JavaScript - Load immediately -->
    <script>
        // Enhanced loading script with language support
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize welcome screen with language support
            initializeWelcomeScreen();

            // Hide loading screen after critical resources load
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => loadingScreen.remove(), 800);
                }
            }, 3000); // Extended time to show the beautiful welcome screen
        });

        function initializeWelcomeScreen() {
            // Add language attributes for translation
            const welcomeTitle = document.querySelector('.welcome-title');
            const brandName = document.querySelector('.brand-name');
            const welcomeSubtitle = document.querySelector('.welcome-subtitle');

            if (welcomeTitle) {
                welcomeTitle.setAttribute('data-en', 'Welcome to');
                welcomeTitle.setAttribute('data-ar', 'مرحباً بك في');
            }

            if (brandName) {
                brandName.setAttribute('data-en', 'Energy.AI');
                brandName.setAttribute('data-ar', 'Energy.AI');
            }

            if (welcomeSubtitle) {
                welcomeSubtitle.setAttribute('data-en', 'Smart Energy Solutions Powered by AI');
                welcomeSubtitle.setAttribute('data-ar', 'حلول الطاقة الذكية مدعومة بالذكاء الاصطناعي');
            }

            // Add additional energy particles for enhanced visual effect
            createEnergyParticles();
        }

        function createEnergyParticles() {
            const particlesContainer = document.querySelector('.energy-particles');
            if (particlesContainer) {
                // Create additional floating particles
                for (let i = 0; i < 6; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'floating-particle';
                    particle.style.cssText = `
                        position: absolute;
                        width: 3px;
                        height: 3px;
                        background: var(--secondary-color);
                        border-radius: 50%;
                        top: ${Math.random() * 100}%;
                        left: ${Math.random() * 100}%;
                        animation: particleFloat ${3 + Math.random() * 2}s ease-in-out infinite;
                        animation-delay: ${Math.random() * 2}s;
                        opacity: 0.7;
                    `;
                    particlesContainer.appendChild(particle);
                }
            }
        }
    </script>



    <!-- Auth system will be loaded by auth-system.js -->

    <!-- Glassmorphism System - Priority Load -->
    <script src="js/glassmorphism.js"></script>

    <!-- Core Systems - Load first -->
    <script src="js/config.js"></script>
    <script src="js/state-management.js"></script>
    <script src="js/security-system.js"></script>
    <script src="js/performance-optimizations.js"></script>
    <script src="js/notification-system.js"></script>
    <script src="js/analytics-system.js"></script>

    <!-- Core JavaScript -->
    <script src="js/jarvis-config.js"></script>
    <script src="js/language.js"></script>
    <script src="js/auth-system.js"></script>
    <script src="js/embedded-map.js"></script>
    <script src="js/main.js"></script>

    <!-- Three.js and Neon Cursor -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.139.2/build/three.module.js",
            "threejs-toys": "https://unpkg.com/threejs-toys@0.0.8/build/threejs-toys.module.cdn.min.js"
        }
    }
    </script>
    <script type="module" src="js/neon-cursor.js"></script>

    <!-- خلفية متحركة مع أشكال هندسية وتأثيرات متقدمة -->
    <div class="animated-background">
        <!-- الطبقة الأساسية للخلفية -->
        <div class="background-layer base-layer"></div>

        <!-- تأثير نبضة واضح -->
        <div class="pulse-effect"></div>

        <!-- الأشكال الهندسية الكبيرة -->
        <div class="geometric-shapes">
            <div class="main-geometric-shape"></div>
            <div class="secondary-shape"></div>
            <div class="tertiary-shape"></div>
            <div class="quaternary-shape"></div>
        </div>

        <!-- الأشكال المتحركة -->
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
            <div class="shape shape-6"></div>
            <div class="shape shape-7"></div>
            <div class="shape shape-8"></div>
        </div>

        <!-- خطوط الطاقة -->
        <div class="energy-lines">
            <div class="energy-line line-1"></div>
            <div class="energy-line line-2"></div>
            <div class="energy-line line-3"></div>
        </div>

        <!-- جسيمات متحركة -->
        <div class="particles">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
        </div>

        <!-- تأثيرات الضوء -->
        <div class="light-effects">
            <div class="light-beam beam-1"></div>
            <div class="light-beam beam-2"></div>
            <div class="light-beam beam-3"></div>
        </div>



    <!-- Enhanced Gemini-style Energy Optimization Chat Modal -->
    <div id="geminiChatModal" class="gemini-chat-modal" aria-hidden="true">
        <div class="gemini-chat-container">
            <header class="gemini-header">
                <div class="gemini-controls">
                    <button id="closeGeminiChat" class="gemini-close" aria-label="إغلاق المحادثة">
                        ✕
                    </button>
                </div>
            </header>

            <div id="geminiMessages" class="gemini-messages" aria-live="polite">
                <div class="gemini-welcome">
                    <h2>مرحباً</h2>
                    <p>كيف يمكنني مساعدتك في تحسين استهلاك الطاقة اليوم؟</p>

                    <div class="gemini-suggestions">
                        <div class="suggestion-card" data-suggestion="تحليل استهلاك الطاقة">
                            <h4>تحليل استهلاك الطاقة</h4>
                            <p>احصل على تحليل مفصل لاستهلاك الطاقة في منزلك أو مكتبك</p>
                        </div>
                        <div class="suggestion-card" data-suggestion="نصائح توفير الطاقة">
                            <h4>نصائح توفير الطاقة</h4>
                            <p>اكتشف طرق فعالة لتقليل فاتورة الكهرباء</p>
                        </div>
                        <div class="suggestion-card" data-suggestion="الطاقة المتجددة">
                            <h4>الطاقة المتجددة</h4>
                            <p>تعرف على حلول الطاقة الشمسية وطاقة الرياح</p>
                        </div>
                        <div class="suggestion-card" data-suggestion="أجهزة ذكية">
                            <h4>الأجهزة الذكية</h4>
                            <p>كيفية استخدام التكنولوجيا الذكية لتحسين كفاءة الطاقة</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="gemini-input-container">
                <div class="gemini-input-wrapper">
                    <textarea
                        id="geminiInput"
                        class="gemini-input"
                        placeholder="اكتب رسالتك هنا..."
                        rows="1"
                    ></textarea>
                    <button id="geminiSend" class="gemini-send-btn" disabled>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>

                <div class="gemini-input-tools">
                    <div class="input-tool">
                        <span>🎤</span>
                        <span>التحدث الصوتي</span>
                    </div>
                    <div class="input-tool">
                        <span>📊</span>
                        <span>Canvas</span>
                    </div>
                    <div class="input-tool">
                        <span>📷</span>
                        <span>صورة</span>
                    </div>
                    <div class="input-tool">
                        <span>🔍</span>
                        <span>Deep Research</span>
                    </div>
                    <div class="input-tool">
                        <span>➕</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simplified Chat Integration Script
        let geminiChat;

        // Main functions for backward compatibility
        function showCustomChat() {
            if (!geminiChat) {
                geminiChat = new GeminiChat();
            }
            geminiChat.open();
        }

        function closeCustomChat() {
            if (geminiChat) {
                geminiChat.close();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Gemini Chat
            geminiChat = new GeminiChat();

            // Bind Energy Optimization button
            const btn = document.getElementById('energyOptimizationBtn');
            if (btn) {
                btn.addEventListener('click', function(e) {
                    console.log('Energy Optimization clicked');
                    showCustomChat();
                });
                btn.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        showCustomChat();
                    }
                });
            } else {
                console.log('energyOptimizationBtn not found');
            }
        });

        // Global error logger to help debugging
        window.addEventListener('error', function(e){
            console.error('Global error captured:', e.error || e.message, e.filename + ':' + e.lineno);
        });

        // Enhanced floating test button with Gemini style
        (function createTestButton(){
            const btn = document.createElement('button');
            btn.id = 'openChatTestBtn';
            btn.innerHTML = '💬 محادثة';
            btn.style.cssText = `
                position: fixed;
                right: 24px;
                bottom: 24px;
                z-index: 10003;
                padding: 12px 20px;
                border-radius: 24px;
                background: linear-gradient(135deg, #58a6ff, #4493e0);
                color: #fff;
                border: none;
                cursor: pointer;
                box-shadow: 0 8px 24px rgba(88, 166, 255, 0.3);
                font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s ease;
                backdrop-filter: blur(10px);
            `;

            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 12px 32px rgba(88, 166, 255, 0.4)';
            });

            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 8px 24px rgba(88, 166, 255, 0.3)';
            });

            document.body.appendChild(btn);
            btn.addEventListener('click', function(){
                console.log('test button clicked');
                showCustomChat();
            });
        })();
    </script>

    <!-- Enhanced Gemini Chat System -->
    <script src="js/gemini-chat.js"></script>
</body>
</html>
