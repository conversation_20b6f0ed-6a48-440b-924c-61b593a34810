<!DOCTYPE html>
<html lang="en" data-theme="hybrid">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Hybrid Energy Design Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #1976d2;
            --primary-orange: #ff7200;
            --dark-bg: #0a0a0a;
            --dark-secondary: #1a1a1a;
            --dark-tertiary: #2a2a2a;
            --light-bg: #ffffff;
            --light-secondary: #f8f9fa;
            --light-tertiary: #e9ecef;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --neu-shadow-dark: #1a1a1a;
            --neu-shadow-light: #3a3a3a;
            --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
            --gradient-secondary: linear-gradient(135deg, #667eea, #764ba2);
            --gradient-accent: linear-gradient(135deg, #f093fb, #f5576c);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Hybrid Background */
        .hybrid-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background:
                radial-gradient(circle at 20% 80%, rgba(25, 118, 210, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 114, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
        }

        /* Grid Overlay */
        .grid-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background-image:
                linear-gradient(rgba(25, 118, 210, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(25, 118, 210, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridFloat 20s ease-in-out infinite;
        }

        @keyframes gridFloat {
            0%, 100% { transform: translate(0, 0); }
            50% { transform: translate(25px, 25px); }
        }

        /* Hybrid Components */
        .glass-neu {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow:
                8px 8px 16px var(--neu-shadow-dark),
                -8px -8px 16px var(--neu-shadow-light),
                inset 0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        .gradient-border {
            position: relative;
            background: var(--dark-bg);
            border-radius: 20px;
        }

        .gradient-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--gradient-primary);
            border-radius: 22px;
            z-index: -1;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: logoShine 3s ease-in-out infinite;
        }

        @keyframes logoShine {
            0%, 100% { left: -100%; }
            50% { left: 100%; }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover {
            color: var(--text-primary);
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 20px rgba(25, 118, 210, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(25, 118, 210, 0.4);
        }

        .btn-hybrid {
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 2px solid transparent;
            background-clip: padding-box;
            position: relative;
        }

        .btn-hybrid::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            border-radius: 15px;
            z-index: -1;
            margin: -2px;
        }

        .btn-hybrid:hover {
            color: white;
            background: var(--gradient-primary);
            box-shadow:
                0 4px 20px rgba(25, 118, 210, 0.3),
                8px 8px 16px var(--neu-shadow-dark),
                -8px -8px 16px var(--neu-shadow-light);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }

        .hero-content {
            max-width: 900px;
            z-index: 2;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4.5rem);
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero .highlight-blue {
            background: linear-gradient(135deg, var(--primary-blue), #42a5f5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero .highlight-orange {
            background: linear-gradient(135deg, var(--primary-orange), #ffb74d);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Floating Elements */
        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            border-radius: 50%;
            animation: floatHybrid 15s infinite ease-in-out;
        }

        .floating-element:nth-child(1) {
            width: 100px;
            height: 100px;
            background: var(--gradient-primary);
            top: 20%;
            left: 10%;
            animation-delay: 0s;
            opacity: 0.1;
        }

        .floating-element:nth-child(2) {
            width: 150px;
            height: 150px;
            background: var(--gradient-secondary);
            top: 60%;
            left: 80%;
            animation-delay: 5s;
            opacity: 0.08;
        }

        .floating-element:nth-child(3) {
            width: 80px;
            height: 80px;
            background: var(--gradient-accent);
            top: 80%;
            left: 20%;
            animation-delay: 10s;
            opacity: 0.12;
        }

        @keyframes floatHybrid {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-50px) rotate(120deg); }
            66% { transform: translateY(50px) rotate(240deg); }
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 60px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            border-radius: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
        }

        .feature-card.glass-style {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
        }

        .feature-card.neu-style {
            background: var(--dark-secondary);
            box-shadow: 8px 8px 16px var(--neu-shadow-dark), -8px -8px 16px var(--neu-shadow-light);
        }

        .feature-card.gradient-style {
            background: var(--gradient-secondary);
            color: white;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            position: relative;
        }

        .glass-style .feature-icon {
            background: var(--gradient-primary);
        }

        .neu-style .feature-icon {
            background: var(--dark-tertiary);
            box-shadow: inset 4px 4px 8px var(--neu-shadow-dark), inset -4px -4px 8px var(--neu-shadow-light);
            color: var(--primary-orange);
        }

        .gradient-style .feature-icon {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--text-primary);
        }

        .gradient-style h3 {
            color: white;
        }

        .feature-card p {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .gradient-style p {
            color: rgba(255, 255, 255, 0.9);
        }

        /* Interactive Demo Section */
        .demo-section {
            padding: 100px 20px;
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
        }

        .demo-controls {
            padding: 40px;
            border-radius: 30px;
            margin-top: 40px;
            position: relative;
        }

        .style-switcher {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .style-btn {
            padding: 10px 20px;
            border: 2px solid var(--primary-blue);
            background: transparent;
            color: var(--primary-blue);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .style-btn.active {
            background: var(--gradient-primary);
            color: white;
            border-color: transparent;
        }

        .demo-preview {
            padding: 40px;
            border-radius: 25px;
            margin-top: 30px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.5s ease;
        }

        .demo-content {
            text-align: center;
        }

        .demo-content h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .demo-content p {
            opacity: 0.8;
        }

        /* Contact Section */
        .contact {
            padding: 100px 20px;
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .contact-form {
            padding: 40px;
            border-radius: 30px;
            margin-top: 40px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px 20px;
            background: var(--dark-secondary);
            border: 2px solid var(--dark-tertiary);
            border-radius: 15px;
            color: var(--text-primary);
            font-size: 1rem;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 20px rgba(25, 118, 210, 0.2);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: var(--text-secondary);
        }

        /* Footer */
        .footer {
            padding: 40px 20px;
            text-align: center;
            margin-top: 50px;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
            border-radius: 25px;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: var(--gradient-primary);
            color: white;
            transform: translateX(-5px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-actions {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .style-switcher {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Hybrid Background -->
    <div class="hybrid-bg"></div>
    <div class="grid-overlay"></div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← Back to Gallery</a>

    <!-- Navigation -->
    <nav class="navbar glass-neu">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">⚡</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#features">Features</a></li>
                <li><a href="#demo">Demo</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <a href="#contact" class="btn btn-primary">Get Started</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
        <div class="hero-content">
            <h1>
                <span class="highlight-blue">Smart Energy</span> meets
                <span class="highlight-orange">Hybrid Design</span>
            </h1>
            <p>Experience the perfect fusion of glassmorphism, neumorphism, gradients, and minimalism in one powerful energy management platform designed specifically for Energy.AI.</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-primary">Explore Hybrid Features</a>
                <a href="#demo" class="btn btn-hybrid">Try Interactive Demo</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">Hybrid Design System</h2>
        <div class="features-grid">
            <div class="feature-card glass-style">
                <div class="feature-icon">🔮</div>
                <h3>Glassmorphism Elements</h3>
                <p>Beautiful glass-like components with blur effects and transparency that create depth and modern aesthetics.</p>
            </div>
            <div class="feature-card neu-style">
                <div class="feature-icon">🌙</div>
                <h3>Neumorphic Controls</h3>
                <p>Soft, tactile interface elements that feel natural to interact with, providing satisfying user feedback.</p>
            </div>
            <div class="feature-card gradient-style">
                <div class="feature-icon">🌈</div>
                <h3>Dynamic Gradients</h3>
                <p>Flowing color transitions that bring energy data to life with vibrant, animated visualizations.</p>
            </div>
            <div class="feature-card glass-style">
                <div class="feature-icon">⚡</div>
                <h3>Smart Analytics</h3>
                <p>AI-powered energy insights presented through our hybrid interface for maximum clarity and engagement.</p>
            </div>
            <div class="feature-card neu-style">
                <div class="feature-icon">🎛️</div>
                <h3>Intuitive Controls</h3>
                <p>Perfectly balanced control panels that combine the best of all design philosophies for optimal usability.</p>
            </div>
            <div class="feature-card gradient-style">
                <div class="feature-icon">🚀</div>
                <h3>Future-Ready</h3>
                <p>Cutting-edge design that evolves with technology while maintaining the Energy.AI brand identity.</p>
            </div>
        </div>
    </section>

    <!-- Interactive Demo Section -->
    <section class="demo-section" id="demo">
        <h2 class="section-title">Interactive Style Demo</h2>
        <p style="color: var(--text-secondary); margin-bottom: 20px;">Experience different design styles in real-time</p>

        <div class="demo-controls glass-neu">
            <h3 style="color: var(--text-primary); margin-bottom: 30px;">Choose Your Style</h3>

            <div class="style-switcher">
                <button class="style-btn active" onclick="switchStyle('glass')">Glassmorphism</button>
                <button class="style-btn" onclick="switchStyle('neu')">Neumorphism</button>
                <button class="style-btn" onclick="switchStyle('gradient')">Gradient</button>
                <button class="style-btn" onclick="switchStyle('hybrid')">Hybrid</button>
            </div>

            <div class="demo-preview glass-neu" id="demo-preview">
                <div class="demo-content">
                    <h3>Glassmorphism Style</h3>
                    <p>Clean, modern glass effects with beautiful transparency and blur</p>
                    <div style="margin-top: 20px;">
                        <button class="btn btn-primary">Sample Button</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <h2 class="section-title">Ready for Hybrid Energy?</h2>
        <p style="color: var(--text-secondary);">Let's create the perfect energy management experience with our hybrid design system</p>

        <form class="contact-form gradient-border">
            <div class="form-group">
                <label for="name">Full Name</label>
                <input type="text" id="name" placeholder="Enter your full name" required>
            </div>
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="company">Company</label>
                <input type="text" id="company" placeholder="Your company name">
            </div>
            <div class="form-group">
                <label for="message">Project Details</label>
                <textarea id="message" rows="5" placeholder="Tell us about your energy management needs and design preferences..." required></textarea>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">
                Start Your Hybrid Journey ✨
            </button>
        </form>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content glass-neu">
            <p style="color: var(--text-primary);">&copy; 2024 Energy.AI - Hybrid Design Excellence</p>
            <p style="margin-top: 10px; color: var(--text-secondary);">The Perfect Fusion of All Design Philosophies</p>
        </div>
    </footer>

    <script>
        // Style Switcher
        function switchStyle(style) {
            const preview = document.getElementById('demo-preview');
            const buttons = document.querySelectorAll('.style-btn');

            // Update active button
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Reset classes
            preview.className = 'demo-preview';

            // Apply new style
            switch(style) {
                case 'glass':
                    preview.classList.add('glass-neu');
                    preview.innerHTML = `
                        <div class="demo-content">
                            <h3>Glassmorphism Style</h3>
                            <p>Clean, modern glass effects with beautiful transparency and blur</p>
                            <div style="margin-top: 20px;">
                                <button class="btn btn-primary">Sample Button</button>
                            </div>
                        </div>
                    `;
                    break;
                case 'neu':
                    preview.style.background = 'var(--dark-secondary)';
                    preview.style.boxShadow = '8px 8px 16px var(--neu-shadow-dark), -8px -8px 16px var(--neu-shadow-light)';
                    preview.innerHTML = `
                        <div class="demo-content">
                            <h3>Neumorphism Style</h3>
                            <p>Soft, tactile design with realistic depth and shadows</p>
                            <div style="margin-top: 20px;">
                                <button class="btn btn-hybrid">Sample Button</button>
                            </div>
                        </div>
                    `;
                    break;
                case 'gradient':
                    preview.style.background = 'var(--gradient-secondary)';
                    preview.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.3)';
                    preview.innerHTML = `
                        <div class="demo-content">
                            <h3 style="color: white;">Gradient Style</h3>
                            <p style="color: rgba(255, 255, 255, 0.9);">Dynamic, colorful gradients that bring energy to life</p>
                            <div style="margin-top: 20px;">
                                <button class="btn" style="background: rgba(255, 255, 255, 0.2); color: white; border: 1px solid rgba(255, 255, 255, 0.3);">Sample Button</button>
                            </div>
                        </div>
                    `;
                    break;
                case 'hybrid':
                    preview.classList.add('gradient-border');
                    preview.style.background = 'var(--dark-bg)';
                    preview.style.boxShadow = '0 20px 40px rgba(25, 118, 210, 0.2)';
                    preview.innerHTML = `
                        <div class="demo-content">
                            <h3 style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Hybrid Style</h3>
                            <p>The perfect fusion of all design philosophies for Energy.AI</p>
                            <div style="margin-top: 20px;">
                                <button class="btn btn-primary">Sample Button</button>
                            </div>
                        </div>
                    `;
                    break;
            }
        }

        // Form Submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            submitBtn.textContent = 'Processing... ⚡';
            submitBtn.style.background = 'var(--gradient-secondary)';

            setTimeout(() => {
                submitBtn.textContent = 'Message Sent! ✅';
                submitBtn.style.background = 'linear-gradient(135deg, #4caf50, #8bc34a)';

                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.style.background = 'var(--gradient-primary)';
                    this.reset();
                }, 2000);
            }, 1500);
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Dynamic Background Effects
        setInterval(() => {
            const elements = document.querySelectorAll('.floating-element');
            elements.forEach(element => {
                const randomDelay = Math.random() * 2000;
                setTimeout(() => {
                    element.style.opacity = Math.random() * 0.15 + 0.05;
                }, randomDelay);
            });
        }, 5000);

        // Navbar Scroll Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(10, 10, 10, 0.9)';
                navbar.style.backdropFilter = 'blur(30px)';
            } else {
                navbar.style.background = 'var(--glass-bg)';
                navbar.style.backdropFilter = 'blur(20px)';
            }
        });

        console.log('🚀 Hybrid Energy Design Demo Loaded Successfully!');
    </script>
</body>
</html>