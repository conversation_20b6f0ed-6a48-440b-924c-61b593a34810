/* تحسين واجهة الدردشة للوضع الفاتح */
.chat-input button:hover {
    background: rgba(25, 118, 210, 0.5);
}

/* تحسين واجهة الدردشة للوضع الفاتح */
[data-theme="light"] .ai-chat-container {
    background: linear-gradient(to bottom, var(--background-secondary), var(--background-tertiary));
    border: 2px solid rgba(25, 118, 210, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .chat-header {
    background: var(--primary-gradient);
    color: #ffffff;
    border-bottom: 2px solid rgba(25, 118, 210, 0.2);
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .chat-messages {
    background: rgba(255, 255, 255, 0.8);
    background-image:
        linear-gradient(rgba(25, 118, 210, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(25, 118, 210, 0.02) 1px, transparent 1px);
}

[data-theme="light"] .chat-messages::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(25, 118, 210, 0.5);
}

[data-theme="light"] .bot .message-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    color: var(--text-primary);
    border: 1px solid rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .user .message-content {
    background: var(--primary-gradient);
    color: #ffffff;
}

[data-theme="light"] .suggested-questions {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(248, 249, 250, 0.9));
    border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .question-btn {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    border: 1px solid rgba(25, 118, 210, 0.2);
    color: var(--text-primary);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .question-btn:hover {
    background: var(--primary-gradient);
    color: #ffffff;
    box-shadow: 0 4px 10px rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .chat-input {
    background: linear-gradient(to top, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
    border-top: 1px solid rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .chat-input::before {
    background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.2), transparent);
}

[data-theme="light"] .chat-input input {
    border: 2px solid rgba(25, 118, 210, 0.2);
    background: rgba(255, 255, 255, 0.7);
    color: var(--text-primary);
}

[data-theme="light"] .chat-input input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
}

[data-theme="light"] .chat-input button {
    background: var(--primary-gradient);
    color: #ffffff;
    box-shadow: 0 3px 10px rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .chat-input button:hover {
    box-shadow: 0 5px 15px rgba(25, 118, 210, 0.4);
}

[data-theme="light"] .typing-indicator {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .typing-indicator::after {
    border-top: 8px solid rgba(255, 255, 255, 0.9);
}

[data-theme="light"] .typing-indicator span {
    background: var(--primary-gradient);
}

/* تحسين الخلفية الهندسية للوضع الفاتح */
[data-theme="light"] .geometric-background {
    background: linear-gradient(135deg,
        #ffffff 0%,
        #f8f9fa 20%,
        #e3f2fd 40%,
        #f1f8e9 60%,
        #fff3e0 80%,
        #ffffff 100%);
}

[data-theme="light"] .main-geometric-shape {
    background: linear-gradient(135deg,
        rgba(25, 118, 210, 0.06) 0%,
        rgba(33, 150, 243, 0.08) 30%,
        rgba(66, 165, 245, 0.05) 60%,
        transparent 100%);
    filter: blur(60px);
}

[data-theme="light"] .secondary-shape {
    background: linear-gradient(45deg,
        rgba(255, 152, 0, 0.04) 0%,
        rgba(255, 193, 7, 0.06) 40%,
        rgba(255, 235, 59, 0.03) 70%,
        transparent 100%);
    filter: blur(50px);
}

[data-theme="light"] .tertiary-shape {
    background: linear-gradient(225deg,
        rgba(76, 175, 80, 0.03) 0%,
        rgba(129, 199, 132, 0.05) 50%,
        transparent 100%);
    filter: blur(70px);
}

[data-theme="light"] .shape {
    background: linear-gradient(45deg,
        rgba(25, 118, 210, 0.08),
        rgba(33, 150, 243, 0.04));
}

[data-theme="light"] .shape-1 {
    background: linear-gradient(135deg,
        rgba(25, 118, 210, 0.12),
        rgba(33, 150, 243, 0.08));
    box-shadow: 0 0 80px rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .shape-2 {
    background: linear-gradient(45deg,
        rgba(255, 152, 0, 0.1),
        rgba(255, 193, 7, 0.06));
    box-shadow: 0 0 60px rgba(255, 152, 0, 0.08);
}

[data-theme="light"] .shape-3 {
    background: linear-gradient(225deg,
        rgba(76, 175, 80, 0.09),
        rgba(129, 199, 132, 0.06));
    box-shadow: 0 0 50px rgba(76, 175, 80, 0.06);
}

[data-theme="light"] .shape-4 {
    background: linear-gradient(315deg,
        rgba(33, 150, 243, 0.08),
        rgba(66, 165, 245, 0.05));
}

[data-theme="light"] .shape-5 {
    background: linear-gradient(180deg,
        rgba(255, 152, 0, 0.1),
        rgba(255, 193, 7, 0.06));
    box-shadow: 0 0 60px rgba(255, 152, 0, 0.08);
}

[data-theme="light"] .particle {
    background: rgba(25, 118, 210, 0.6);
    box-shadow: 0 0 8px rgba(25, 118, 210, 0.4);
}

.api-tester-icon {
    background: rgba(255, 255, 255, 0.2);
    color: var(--secondary-color);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    z-index: 10;
    position: relative;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

.api-icon {
    width: 70px;
    height: 70px;
    transition: all 0.3s ease;
}

.design-image-link:hover .api-tester-icon {
    background: rgba(255, 255, 255, 0.5);
    transform: scale(1.15);
    border-color: var(--secondary-color);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.design-image-link:hover .api-icon {
    transform: scale(1.15);
    filter: brightness(1.2);
}

.api-link-container {
    display: flex;
    justify-content: center;
    margin: 15px 0;
}

.api-tester-link {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 15px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 114, 0, 0.3);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .api-tester-link {
    color: #42A5F5;
}

.api-link-icon {
    margin-left: 8px;
    font-size: 18px;
}

.api-link-text {
    font-size: 14px;
}

.api-tester-link:hover {
    background: rgba(255, 114, 0, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.design-image::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, var(--card-bg) 0%, transparent 100%);
}

.design-card h3 {
    padding: 20px 20px 10px;
    font-size: 22px;
    color: var(--primary-color);
}

.design-card p {
    padding: 0 20px 20px;
    color: var(--text-color);
    font-size: 16px;
    line-height: 1.5;
}

/* Contact Section - Enhanced Design */
.contact-section {
    background: linear-gradient(135deg,
        var(--background-primary) 0%,
        var(--background-secondary) 50%,
        var(--background-tertiary) 100%);
    position: relative;
    overflow: hidden;
    padding: 100px 40px;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 20%, rgba(255, 114, 0, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 165, 0, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.contact-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.contact-header h2 {
    font-size: 48px;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    filter: drop-shadow(0 4px 8px var(--shadow-orange));
}

.contact-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.contact-container {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 60px;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}


