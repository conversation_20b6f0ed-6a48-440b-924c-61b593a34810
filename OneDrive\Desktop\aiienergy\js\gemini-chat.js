/**
 * Enhanced Gemini-style Chat System for Energy.AI
 * نظام المحادثة المحسن بنمط Gemini لموقع Energy.AI
 */

class GeminiChat {
    constructor() {
        this.modal = document.getElementById('geminiChatModal');
        this.messagesContainer = document.getElementById('geminiMessages');
        this.input = document.getElementById('geminiInput');
        this.sendBtn = document.getElementById('geminiSend');
        this.closeBtn = document.getElementById('closeGeminiChat');
        this.isTyping = false;
        this.conversationStarted = false;
        this.apiEndpoint = '/api/chat'; // Configure your API endpoint
        
        this.init();
    }
    
    init() {
        if (!this.modal) {
            console.warn('Gemini Chat modal not found');
            return;
        }
        
        // Event listeners
        this.closeBtn?.addEventListener('click', () => this.close());
        this.sendBtn?.addEventListener('click', () => this.sendMessage());
        this.input?.addEventListener('input', () => this.handleInputChange());
        this.input?.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Suggestion cards
        this.setupSuggestions();
        
        // Auto-resize textarea
        this.setupAutoResize();
        
        console.log('✨ Gemini Chat initialized');
    }
    
    setupSuggestions() {
        const suggestions = document.querySelectorAll('.suggestion-card');
        suggestions.forEach(card => {
            card.addEventListener('click', () => {
                const suggestion = card.getAttribute('data-suggestion');
                if (suggestion) {
                    this.input.value = suggestion;
                    this.handleInputChange();
                    this.sendMessage();
                }
            });
        });
    }
    
    setupAutoResize() {
        if (!this.input) return;
        
        this.input.addEventListener('input', () => {
            this.input.style.height = 'auto';
            this.input.style.height = Math.min(this.input.scrollHeight, 120) + 'px';
        });
    }
    
    handleInputChange() {
        const hasText = this.input.value.trim().length > 0;
        this.sendBtn.disabled = !hasText;
    }
    
    handleKeyDown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (!this.sendBtn.disabled) {
                this.sendMessage();
            }
        }
    }
    
    open() {
        if (!this.modal) return;
        
        this.modal.setAttribute('aria-hidden', 'false');
        this.modal.classList.add('open');
        
        // Focus input after animation
        setTimeout(() => {
            this.input?.focus();
        }, 300);
        
        console.log('Gemini chat opened');
    }
    
    close() {
        if (!this.modal) return;
        
        this.modal.setAttribute('aria-hidden', 'true');
        this.modal.classList.remove('open');
        
        // Reset conversation
        this.reset();
        
        console.log('Gemini chat closed');
    }
    
    reset() {
        this.conversationStarted = false;
        this.input.value = '';
        this.input.style.height = 'auto';
        this.sendBtn.disabled = true;
        
        // Show welcome screen again
        const welcome = document.querySelector('.gemini-welcome');
        if (welcome) {
            welcome.style.display = 'block';
        }
        
        // Clear conversation messages
        const messages = this.messagesContainer.querySelectorAll('.message-bubble');
        messages.forEach(msg => msg.remove());
    }
    
    async sendMessage() {
        const text = this.input.value.trim();
        if (!text || this.isTyping) return;
        
        // Hide welcome screen on first message
        if (!this.conversationStarted) {
            const welcome = document.querySelector('.gemini-welcome');
            if (welcome) {
                welcome.style.display = 'none';
            }
            this.conversationStarted = true;
        }
        
        // Add user message
        this.addUserMessage(text);
        
        // Clear input
        this.input.value = '';
        this.input.style.height = 'auto';
        this.sendBtn.disabled = true;
        
        // Show typing indicator and get response
        this.showTypingIndicator();
        
        try {
            // Try to get real API response first
            const response = await this.getAPIResponse(text);
            this.hideTypingIndicator();
            this.addAssistantMessage(response);
        } catch (error) {
            console.warn('API call failed, using fallback response:', error);
            // Fallback to simulated response
            this.simulateResponse(text);
        }
    }
    
    async getAPIResponse(message) {
        const response = await fetch(this.apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                context: 'energy_optimization',
                language: 'ar'
            })
        });
        
        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }
        
        const data = await response.json();
        return data.response || data.message || 'عذراً، لم أتمكن من معالجة طلبك.';
    }
    
    addUserMessage(text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message-bubble user';
        messageDiv.textContent = text;
        
        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    addAssistantMessage(text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message-bubble assistant';
        messageDiv.innerHTML = this.formatMessage(text);
        
        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    formatMessage(text) {
        // Enhanced markdown-like formatting
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^(.*)$/, '<p>$1</p>')
            .replace(/<p><\/p>/g, '')
            .replace(/• /g, '<li>')
            .replace(/<li>(.*?)(<br>|<\/p>)/g, '<li>$1</li>')
            .replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>');
    }
    
    showTypingIndicator() {
        this.isTyping = true;
        
        const typingDiv = document.createElement('div');
        typingDiv.className = 'typing-indicator';
        typingDiv.innerHTML = `
            <span>Energy.AI يكتب</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;
        
        this.messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        const typingIndicator = this.messagesContainer.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
        this.isTyping = false;
    }
    
    simulateResponse(userMessage) {
        // Simulate API delay
        setTimeout(() => {
            this.hideTypingIndicator();
            
            // Generate contextual response based on user input
            const response = this.generateResponse(userMessage);
            this.addAssistantMessage(response);
        }, 1500 + Math.random() * 1000);
    }
    
    generateResponse(userMessage) {
        const message = userMessage.toLowerCase();
        
        // Energy optimization responses in Arabic
        if (message.includes('تحليل') || message.includes('استهلاك')) {
            return `**تحليل استهلاك الطاقة** ⚡

يمكنني مساعدتك في تحليل استهلاك الطاقة من خلال:

• **مراقبة الاستهلاك اليومي**: تتبع أنماط استخدام الكهرباء
• **تحديد الأجهزة الأكثر استهلاكاً**: معرفة مصادر الهدر الرئيسية  
• **مقارنة الفترات الزمنية**: تحليل التغيرات الشهرية والسنوية
• **توصيات التحسين**: اقتراحات عملية لتوفير الطاقة

هل تريد البدء بتحليل نوع معين من الأجهزة أو المساحات؟`;
        }
        
        if (message.includes('توفير') || message.includes('نصائح')) {
            return `**نصائح توفير الطاقة** 💡

إليك أهم الطرق لتقليل استهلاك الطاقة:

**الإضاءة:**
• استخدم مصابيح LED بدلاً من المصابيح التقليدية
• اطفئ الأنوار عند عدم الحاجة إليها

**التكييف والتدفئة:**
• اضبط درجة الحرارة على 24°م في الصيف و20°م في الشتاء
• استخدم المراوح لتحسين توزيع الهواء

**الأجهزة الكهربائية:**
• افصل الأجهزة غير المستخدمة من الكهرباء
• اختر الأجهزة ذات تصنيف الطاقة العالي

هل تريد نصائح محددة لجهاز أو منطقة معينة؟`;
        }
        
        if (message.includes('شمسية') || message.includes('متجددة')) {
            return `**الطاقة المتجددة** 🌞

الطاقة الشمسية خيار ممتاز لتوفير المال وحماية البيئة:

**المزايا:**
• توفير 50-90% من فاتورة الكهرباء
• استثمار طويل المدى مع عائد مضمون
• صديقة للبيئة وتقلل انبعاثات الكربون

**الأنواع:**
• **الأنظمة المتصلة بالشبكة**: لتوفير فاتورة الكهرباء
• **الأنظمة المنفصلة**: للمناطق النائية
• **الأنظمة الهجينة**: تجمع بين المزايا

**التكلفة:** تتراوح من 15,000-50,000 ريال حسب حجم النظام

هل تريد حساب التكلفة والوفورات لمنزلك؟`;
        }
        
        if (message.includes('ذكية') || message.includes('تكنولوجيا')) {
            return `**الأجهزة الذكية لتوفير الطاقة** 🏠

التكنولوجيا الذكية تساعد في تحسين كفاءة الطاقة:

**منظمات الحرارة الذكية:**
• توفر 10-15% من تكاليف التدفئة والتبريد
• تتعلم من عاداتك وتضبط الحرارة تلقائياً

**المقابس الذكية:**
• تراقب استهلاك كل جهاز
• تطفئ الأجهزة تلقائياً عند عدم الاستخدام

**أنظمة الإضاءة الذكية:**
• تضبط الإضاءة حسب الوقت والحاجة
• توفر حتى 60% من استهلاك الإضاءة

**أجهزة المراقبة:**
• تعطيك تقارير مفصلة عن الاستهلاك
• تنبهك عند وجود استهلاك غير طبيعي

أي نوع من الأجهزة الذكية يهمك أكثر؟`;
        }
        
        // Default response
        return `شكراً لسؤالك! 🌟

أنا مساعد Energy.AI المتخصص في تحسين استهلاك الطاقة. يمكنني مساعدتك في:

• **تحليل استهلاك الطاقة** وتحديد مصادر الهدر
• **نصائح توفير الطاقة** العملية والفعالة  
• **حلول الطاقة المتجددة** مثل الألواح الشمسية
• **الأجهزة الذكية** لتحسين كفاءة الطاقة
• **حسابات التوفير** والعائد على الاستثمار

كيف يمكنني مساعدتك اليوم؟`;
    }
    
    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GeminiChat;
}

// Global instance
window.GeminiChat = GeminiChat;
