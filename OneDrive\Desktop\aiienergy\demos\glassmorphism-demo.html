<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Modern Glassmorphism Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            background-attachment: fixed;
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 114, 0, 0.1), rgba(25, 118, 210, 0.1));
            border-radius: 50%;
            animation: float 20s infinite linear;
        }

        .shape:nth-child(1) { width: 80px; height: 80px; top: 20%; left: 10%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 120px; height: 120px; top: 60%; left: 80%; animation-delay: 5s; }
        .shape:nth-child(3) { width: 60px; height: 60px; top: 80%; left: 20%; animation-delay: 10s; }
        .shape:nth-child(4) { width: 100px; height: 100px; top: 30%; left: 70%; animation-delay: 15s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(30px) rotate(240deg); }
        }

        /* Glass Components */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            padding: 15px 30px;
            width: 90%;
            max-width: 1200px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ff7200, #1976d2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 8px 16px;
            border-radius: 10px;
        }

        .nav-menu a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .nav-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff7200, #ff9500);
            color: white;
            box-shadow: 0 4px 20px rgba(255, 114, 0, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(255, 114, 0, 0.4);
        }

        .btn-glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }

        .hero-content {
            max-width: 800px;
            z-index: 2;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff, #ff7200, #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 60px;
            background: linear-gradient(135deg, #ffffff, #ff7200);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 114, 0, 0.1), transparent);
            transition: left 0.5s;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff7200, #1976d2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            box-shadow: 0 10px 30px rgba(255, 114, 0, 0.3);
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: white;
        }

        .feature-card p {
            opacity: 0.8;
            line-height: 1.6;
        }

        /* Stats Section */
        .stats {
            padding: 80px 20px;
            text-align: center;
        }

        .stats-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 60px 40px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ff7200, #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        /* Contact Section */
        .contact {
            padding: 100px 20px;
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .contact-form {
            padding: 40px;
            margin-top: 40px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ff7200;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(255, 114, 0, 0.2);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* Footer */
        .footer {
            padding: 40px 20px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Theme Toggle */
        .theme-toggle {
            width: 50px;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .theme-toggle.active {
            background: #ff7200;
        }

        .theme-toggle.active::before {
            transform: translateX(25px);
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }
    </style>
</head>
<body>
    <!-- Background Animation -->
    <div class="bg-animation">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← العودة للقائمة</a>

    <!-- Navigation -->
    <nav class="navbar glass">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">⚡</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">الميزات</a></li>
                <li><a href="#stats">الإحصائيات</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            <div class="nav-actions">
                <div class="theme-toggle" onclick="toggleTheme()"></div>
                <a href="#contact" class="btn btn-primary">ابدأ الآن</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>مستقبل الطاقة الذكية</h1>
            <p>اكتشف حلول الطاقة المدعومة بالذكاء الاصطناعي التي تحول طريقة استهلاكك وإدارتك للطاقة</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-primary">استكشف الحلول</a>
                <a href="#contact" class="btn btn-glass">احجز استشارة مجانية</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">ميزات متقدمة</h2>
        <div class="features-grid">
            <div class="feature-card glass">
                <div class="feature-icon">🧠</div>
                <h3>ذكاء اصطناعي متقدم</h3>
                <p>خوارزميات تعلم آلي متطورة تحلل أنماط استهلاك الطاقة وتقدم توصيات ذكية لتحسين الكفاءة</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">📊</div>
                <h3>تحليلات في الوقت الفعلي</h3>
                <p>مراقبة مستمرة لاستهلاك الطاقة مع تقارير تفصيلية وتنبيهات فورية للتحسين المستمر</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">🌱</div>
                <h3>حلول مستدامة</h3>
                <p>تقنيات صديقة للبيئة تقلل البصمة الكربونية وتعزز استخدام مصادر الطاقة المتجددة</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">💰</div>
                <h3>توفير في التكاليف</h3>
                <p>تحسين استهلاك الطاقة يؤدي إلى توفير يصل إلى 30% من فواتير الكهرباء الشهرية</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">🔒</div>
                <h3>أمان متقدم</h3>
                <p>حماية عالية المستوى لبيانات الطاقة مع تشفير متقدم وأنظمة أمان متعددة الطبقات</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">📱</div>
                <h3>تحكم ذكي</h3>
                <p>تطبيق موبايل متقدم للتحكم في جميع أنظمة الطاقة عن بُعد مع واجهة سهلة الاستخدام</p>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats" id="stats">
        <div class="stats-container glass-strong">
            <h2 class="section-title">أرقام تتحدث عن نفسها</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">عميل راضٍ</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">30%</div>
                    <div class="stat-label">توفير في الطاقة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">دعم فني</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">وقت تشغيل</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <h2 class="section-title">ابدأ رحلتك معنا</h2>
        <p>احصل على استشارة مجانية واكتشف كيف يمكن لحلولنا تحسين كفاءة الطاقة لديك</p>
        
        <form class="contact-form glass">
            <div class="form-group">
                <label for="name">الاسم الكامل</label>
                <input type="text" id="name" placeholder="أدخل اسمك الكامل" required>
            </div>
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="phone">رقم الهاتف</label>
                <input type="tel" id="phone" placeholder="+962 79 XXX XXXX">
            </div>
            <div class="form-group">
                <label for="message">الرسالة</label>
                <textarea id="message" rows="5" placeholder="أخبرنا عن احتياجاتك في مجال الطاقة..." required></textarea>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">
                إرسال الطلب ✨
            </button>
        </form>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content glass">
            <p>&copy; 2024 Energy.AI - جميع الحقوق محفوظة</p>
            <p style="margin-top: 10px; opacity: 0.7;">تصميم Modern Glassmorphism - نسخة تجريبية</p>
        </div>
    </footer>

    <script>
        // Theme Toggle
        function toggleTheme() {
            const toggle = document.querySelector('.theme-toggle');
            const body = document.body;
            
            toggle.classList.toggle('active');
            
            if (toggle.classList.contains('active')) {
                body.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 25%, #dee2e6 50%, #ced4da 75%, #adb5bd 100%)';
                document.documentElement.style.setProperty('--text-color', '#333');
            } else {
                body.style.background = 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%)';
                document.documentElement.style.setProperty('--text-color', '#fff');
            }
        }

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Form Submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Simulate form submission
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = 'جاري الإرسال... ⏳';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                submitBtn.innerHTML = 'تم الإرسال بنجاح! ✅';
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    this.reset();
                }, 2000);
            }, 2000);
        });

        // Navbar Scroll Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.15)';
                navbar.style.backdropFilter = 'blur(30px)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.1)';
                navbar.style.backdropFilter = 'blur(20px)';
            }
        });

        // Animate Stats on Scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        const finalValue = stat.textContent;
                        const numericValue = parseInt(finalValue.replace(/\D/g, ''));
                        const suffix = finalValue.replace(/[\d]/g, '');
                        
                        let currentValue = 0;
                        const increment = numericValue / 50;
                        
                        const timer = setInterval(() => {
                            currentValue += increment;
                            if (currentValue >= numericValue) {
                                stat.textContent = finalValue;
                                clearInterval(timer);
                            } else {
                                stat.textContent = Math.floor(currentValue) + suffix;
                            }
                        }, 30);
                    });
                }
            });
        }, observerOptions);

        const statsSection = document.querySelector('.stats');
        if (statsSection) {
            observer.observe(statsSection);
        }

        console.log('🔮 Modern Glassmorphism Demo Loaded Successfully!');
    </script>
</body>
</html>
