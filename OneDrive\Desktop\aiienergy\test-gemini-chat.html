<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Gemini Chat - Energy.AI</title>
    
    <!-- Enhanced <PERSON> Chat Styles -->
    <link rel="stylesheet" href="css/gemini-chat.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .test-button {
            background: linear-gradient(135deg, #58a6ff, #4493e0);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin: 10px;
            transition: all 0.2s ease;
            box-shadow: 0 8px 24px rgba(88, 166, 255, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(88, 166, 255, 0.4);
        }
        
        .feature-list {
            text-align: right;
            margin: 40px 0;
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-list h3 {
            color: #58a6ff;
            margin-bottom: 15px;
        }
        
        .feature-list ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✨";
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 اختبار واجهة المحادثة الجديدة</h1>
        <p>واجهة محادثة متقدمة بنمط Google Gemini لموقع Energy.AI</p>
        
        <div>
            <button class="test-button" onclick="openGeminiChat()">
                ✨ فتح المحادثة
            </button>
            
            <button class="test-button" onclick="testSuggestion()">
                💡 اختبار الاقتراحات
            </button>
            
            <button class="test-button" onclick="testTyping()">
                ⌨️ اختبار الكتابة
            </button>
        </div>
        
        <div class="feature-list">
            <h3>الميزات الجديدة:</h3>
            <ul>
                <li>تصميم حديث يشبه Google Gemini</li>
                <li>شاشة ترحيب مع اقتراحات تفاعلية</li>
                <li>مؤشر الكتابة أثناء انتظار الرد</li>
                <li>حقل إدخال يتوسع تلقائياً</li>
                <li>ردود ذكية حسب السياق</li>
                <li>دعم كامل للغة العربية</li>
                <li>تأثيرات بصرية متقدمة</li>
                <li>متجاوب مع جميع الشاشات</li>
            </ul>
        </div>
        
        <div class="feature-list">
            <h3>اختبارات سريعة:</h3>
            <ul>
                <li>جرب كتابة "تحليل استهلاك الطاقة"</li>
                <li>جرب كتابة "نصائح توفير الطاقة"</li>
                <li>جرب كتابة "الطاقة المتجددة"</li>
                <li>جرب كتابة "الأجهزة الذكية"</li>
                <li>انقر على بطاقات الاقتراحات</li>
                <li>جرب الكتابة بـ Enter</li>
            </ul>
        </div>
    </div>

    <!-- Enhanced Gemini-style Energy Optimization Chat Modal -->
    <div id="geminiChatModal" class="gemini-chat-modal" aria-hidden="true">
        <div class="gemini-chat-container">
            <header class="gemini-header">
                <div class="gemini-controls">
                    <button id="closeGeminiChat" class="gemini-close" aria-label="إغلاق المحادثة">
                        ✕
                    </button>
                </div>
            </header>
            
            <div id="geminiMessages" class="gemini-messages" aria-live="polite">
                <div class="gemini-welcome">
                    <h2>مرحباً</h2>
                    <p>كيف يمكنني مساعدتك في تحسين استهلاك الطاقة اليوم؟</p>
                    
                    <div class="gemini-suggestions">
                        <div class="suggestion-card" data-suggestion="تحليل استهلاك الطاقة">
                            <h4>تحليل استهلاك الطاقة</h4>
                            <p>احصل على تحليل مفصل لاستهلاك الطاقة في منزلك أو مكتبك</p>
                        </div>
                        <div class="suggestion-card" data-suggestion="نصائح توفير الطاقة">
                            <h4>نصائح توفير الطاقة</h4>
                            <p>اكتشف طرق فعالة لتقليل فاتورة الكهرباء</p>
                        </div>
                        <div class="suggestion-card" data-suggestion="الطاقة المتجددة">
                            <h4>الطاقة المتجددة</h4>
                            <p>تعرف على حلول الطاقة الشمسية وطاقة الرياح</p>
                        </div>
                        <div class="suggestion-card" data-suggestion="أجهزة ذكية">
                            <h4>الأجهزة الذكية</h4>
                            <p>كيفية استخدام التكنولوجيا الذكية لتحسين كفاءة الطاقة</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="gemini-input-container">
                <div class="gemini-input-wrapper">
                    <textarea 
                        id="geminiInput" 
                        class="gemini-input" 
                        placeholder="اسأل Gemini"
                        rows="1"
                    ></textarea>
                    <button id="geminiSend" class="gemini-send-btn" disabled>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
                
                <div class="gemini-input-tools">
                    <div class="input-tool">
                        <span>🎤</span>
                        <span>التحدث الصوتي</span>
                    </div>
                    <div class="input-tool">
                        <span>📊</span>
                        <span>Canvas</span>
                    </div>
                    <div class="input-tool">
                        <span>📷</span>
                        <span>صورة</span>
                    </div>
                    <div class="input-tool">
                        <span>🔍</span>
                        <span>Deep Research</span>
                    </div>
                    <div class="input-tool">
                        <span>➕</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Gemini Chat System -->
    <script src="js/gemini-chat.js"></script>
    
    <script>
        let geminiChat;
        
        function openGeminiChat() {
            if (!geminiChat) {
                geminiChat = new GeminiChat();
            }
            geminiChat.open();
        }
        
        function testSuggestion() {
            openGeminiChat();
            setTimeout(() => {
                const suggestion = document.querySelector('.suggestion-card');
                if (suggestion) {
                    suggestion.click();
                }
            }, 500);
        }
        
        function testTyping() {
            openGeminiChat();
            setTimeout(() => {
                const input = document.getElementById('geminiInput');
                if (input) {
                    input.value = 'مرحبا، كيف يمكنني توفير الطاقة؟';
                    input.dispatchEvent(new Event('input'));
                    input.focus();
                }
            }, 500);
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Test page loaded - Gemini Chat ready!');
        });
    </script>
</body>
</html>
